import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Eye, EyeOff, Download, RefreshCw, Move } from "lucide-react";
import { useWebSocket } from "@/hooks/use-websocket";
import { queryClient } from "@/lib/queryClient";
import Draggable from "react-draggable";

interface CardSummary {
  id: string;
  title: string;
  status: string;
  project: string;
  order: string;
}

interface CardsSummaryProps {
  selectedProject?: string;
  className?: string;
}

const statusColors = {
  'not-started': 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
  'blocked': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
  'in-progress': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  'complete': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  'verified': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
};

const statusLabels = {
  'not-started': 'Not Started',
  'blocked': 'Blocked',
  'in-progress': 'In Progress',
  'complete': 'Complete',
  'verified': 'Verified'
};

// Load saved state from localStorage
const loadSummaryState = () => {
  try {
    const savedState = localStorage.getItem('cards-summary-state');
    if (savedState) {
      return JSON.parse(savedState);
    }
  } catch (error) {
    console.warn('Failed to load summary state:', error);
  }
  return { isVisible: false, position: { x: 0, y: 0 } }; // Collapsed by default
};

// Save state to localStorage
const saveSummaryState = (state: { isVisible: boolean; position: { x: number; y: number } }) => {
  try {
    localStorage.setItem('cards-summary-state', JSON.stringify(state));
  } catch (error) {
    console.warn('Failed to save summary state:', error);
  }
};

export function CardsSummary({ selectedProject, className }: CardsSummaryProps) {
  const [summaryState, setSummaryState] = useState(loadSummaryState);
  const { isVisible, position } = summaryState;
  
  const { data: summary = [], isLoading, refetch } = useQuery({
    queryKey: ['/api/cards/summary', selectedProject],
    queryFn: () => 
      fetch(`/api/cards/summary${selectedProject ? `?project=${encodeURIComponent(selectedProject)}` : ''}`)
        .then(res => res.json()) as Promise<CardSummary[]>
  });

  // Listen for WebSocket updates to refresh summary
  useWebSocket({
    onMessage: (message: any) => {
      if (['CARD_CREATED', 'CARD_UPDATED', 'CARD_DELETED', 'CARDS_BULK_DELETED'].includes(message.type)) {
        queryClient.invalidateQueries({ queryKey: ['/api/cards/summary'] });
      }
    }
  });

  // Group cards by status
  const groupedCards = summary.reduce((acc, card) => {
    if (!acc[card.status]) {
      acc[card.status] = [];
    }
    acc[card.status].push(card);
    return acc;
  }, {} as Record<string, CardSummary[]>);

  // Sort cards within each status by order
  Object.keys(groupedCards).forEach(status => {
    groupedCards[status].sort((a, b) => parseInt(a.order) - parseInt(b.order));
  });

  const downloadMarkdown = async () => {
    try {
      const response = await fetch(`/api/cards/summary/markdown${selectedProject ? `?project=${encodeURIComponent(selectedProject)}` : ''}`);
      const markdown = await response.text();
      
      const blob = new Blob([markdown], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `cards-summary${selectedProject ? `-${selectedProject.toLowerCase().replace(/\s+/g, '-')}` : ''}.md`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download markdown:', error);
    }
  };

  const totalCards = summary.length;

  // Update state and save to localStorage
  const updateSummaryState = (newState: Partial<{ isVisible: boolean; position: { x: number; y: number } }>) => {
    const updatedState = { ...summaryState, ...newState };
    setSummaryState(updatedState);
    saveSummaryState(updatedState);
  };

  if (!isVisible) {
    return (
      <Draggable
        position={position}
        onStop={(e, data) => updateSummaryState({ position: { x: data.x, y: data.y } })}
      >
        <div className={`fixed top-4 right-4 z-50 ${className}`} style={{ position: 'fixed', transform: `translate(${position.x}px, ${position.y}px)` }}>
          <Button
            variant="outline"
            size="sm"
            onClick={() => updateSummaryState({ isVisible: true })}
            className="shadow-lg"
          >
            <Eye className="h-4 w-4 mr-2" />
            Show Summary
          </Button>
        </div>
      </Draggable>
    );
  }

  return (
    <Draggable
      position={position}
      onStop={(e, data) => updateSummaryState({ position: { x: data.x, y: data.y } })}
      handle=".drag-handle"
    >
      <div className={`fixed top-4 right-4 z-50 w-80 ${className}`} style={{ position: 'fixed', transform: `translate(${position.x}px, ${position.y}px)` }}>
        <Card className="shadow-lg border border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Move className="h-4 w-4 text-muted-foreground cursor-move drag-handle" />
                <CardTitle className="text-lg">
                  Cards Summary
                  {totalCards > 0 && (
                    <Badge variant="secondary" className="ml-2">
                      {totalCards}
                    </Badge>
                  )}
                </CardTitle>
              </div>
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => refetch()}
                  disabled={isLoading}
                  className="h-8 w-8 p-0"
                >
                  <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={downloadMarkdown}
                  className="h-8 w-8 p-0"
                >
                  <Download className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => updateSummaryState({ isVisible: false })}
                  className="h-8 w-8 p-0"
                >
                  <EyeOff className="h-4 w-4" />
                </Button>
              </div>
            </div>
            {selectedProject && (
              <p className="text-sm text-foreground/70 font-medium">
                Project: {selectedProject}
              </p>
            )}
          </CardHeader>
          <CardContent className="space-y-4 max-h-96 overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center justify-center py-4">
                <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                <span className="text-sm text-foreground/70">Loading...</span>
              </div>
            ) : totalCards === 0 ? (
              <p className="text-sm text-foreground/70 text-center py-4">
                No cards found
              </p>
            ) : (
              Object.entries(groupedCards).map(([status, cards]) => (
                <div key={status} className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge 
                      className={statusColors[status as keyof typeof statusColors]} 
                      variant="secondary"
                    >
                    {statusLabels[status as keyof typeof statusLabels] || status}
                  </Badge>
                  <span className="text-xs text-foreground/70 font-medium">
                    {cards.length}
                  </span>
                </div>
                <div className="space-y-1">
                  {cards.map((card) => (
                    <div 
                      key={card.id}
                      className="text-sm p-2 rounded border bg-muted/30 hover:bg-muted/50 transition-colors"
                    >
                      <div className="font-medium truncate" title={card.title}>
                        {card.title}
                      </div>
                      {card.project && card.project !== selectedProject && (
                        <div className="text-xs text-foreground/60 font-medium">
                          {card.project}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))
          )}
        </CardContent>
      </Card>
      </div>
    </Draggable>
  );
}