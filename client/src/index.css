@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Modern vibrant background gradient */
  --background: linear-gradient(135deg, hsl(240, 100%, 99%) 0%, hsl(210, 100%, 98%) 100%);
  --background-solid: hsl(240, 100%, 99%);
  --foreground: hsl(220, 15%, 8%);
  --muted: hsl(220, 13%, 91%);
  --muted-foreground: hsl(220, 9%, 46%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(220, 15%, 8%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(220, 15%, 8%);
  --border: hsl(220, 13%, 91%);
  --input: hsl(220, 13%, 91%);
  --primary: linear-gradient(135deg, hsl(221, 83%, 53%) 0%, hsl(230, 89%, 65%) 100%);
  --primary-solid: hsl(221, 83%, 53%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(220, 14%, 96%);
  --secondary-foreground: hsl(220, 9%, 9%);
  --accent: hsl(220, 14%, 96%);
  --accent-foreground: hsl(220, 9%, 9%);
  --destructive: linear-gradient(135deg, hsl(0, 84%, 60%) 0%, hsl(0, 70%, 50%) 100%);
  --destructive-solid: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(221, 83%, 53%);
  --radius: 0.75rem;
  
  /* Vibrant Kanban status colors with gradients */
  --status-not-started: linear-gradient(135deg, hsl(220, 14%, 70%) 0%, hsl(215, 16%, 65%) 100%);
  --status-not-started-solid: hsl(220, 14%, 70%);
  --status-blocked: linear-gradient(135deg, hsl(0, 84%, 60%) 0%, hsl(10, 90%, 58%) 100%);
  --status-blocked-solid: hsl(0, 84%, 60%);
  --status-in-progress: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(210, 100%, 56%) 100%);
  --status-in-progress-solid: hsl(217, 91%, 60%);
  --status-complete: linear-gradient(135deg, hsl(142, 76%, 36%) 0%, hsl(158, 64%, 52%) 100%);
  --status-complete-solid: hsl(142, 76%, 36%);
  --status-verified: linear-gradient(135deg, hsl(262, 83%, 58%) 0%, hsl(280, 100%, 70%) 100%);
  --status-verified-solid: hsl(262, 83%, 58%);
}

.dark {
  /* Dark mode background gradient */
  --background: linear-gradient(135deg, hsl(240, 10%, 3.9%) 0%, hsl(235, 15%, 5%) 100%);
  --background-solid: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 8%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 8%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: linear-gradient(135deg, hsl(210, 90%, 50%) 0%, hsl(220, 89%, 60%) 100%);
  --primary-solid: hsl(210, 90%, 50%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: linear-gradient(135deg, hsl(0, 62.8%, 50%) 0%, hsl(10, 70%, 45%) 100%);
  --destructive-solid: hsl(0, 62.8%, 50%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.75rem;
  
  /* Dark mode Kanban status colors */
  --status-not-started: linear-gradient(135deg, hsl(220, 14%, 40%) 0%, hsl(215, 16%, 35%) 100%);
  --status-not-started-solid: hsl(220, 14%, 40%);
  --status-blocked: linear-gradient(135deg, hsl(0, 70%, 50%) 0%, hsl(10, 80%, 45%) 100%);
  --status-blocked-solid: hsl(0, 70%, 50%);
  --status-in-progress: linear-gradient(135deg, hsl(217, 80%, 55%) 0%, hsl(210, 90%, 50%) 100%);
  --status-in-progress-solid: hsl(217, 80%, 55%);
  --status-complete: linear-gradient(135deg, hsl(142, 70%, 45%) 0%, hsl(158, 60%, 50%) 100%);
  --status-complete-solid: hsl(142, 70%, 45%);
  --status-verified: linear-gradient(135deg, hsl(262, 80%, 65%) 0%, hsl(280, 90%, 70%) 100%);
  --status-verified-solid: hsl(262, 80%, 65%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased text-foreground;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    background: var(--background);
    min-height: 100vh;
  }
}

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Enhanced Kanban specific styles */
.kanban-column {
  @apply transition-all duration-300 ease-in-out;
  background: linear-gradient(145deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.95) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.task-card {
  @apply transition-all duration-300 ease-in-out;
  transform-origin: center;
  background: linear-gradient(145deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.98) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.3);
  box-shadow: 0 4px 20px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.1);
}

.task-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0,0,0,0.15), 0 4px 12px rgba(0,0,0,0.1);
  border: 1px solid rgba(99, 102, 241, 0.2);
}

/* Drag and drop animations */
.sortable-ghost {
  @apply opacity-30;
}

.sortable-chosen {
  @apply scale-105 rotate-1;
}

/* Smooth card transitions */
.task-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Prevent layout shift during drag */
[data-dnd-kit-dropped] {
  animation: card-drop 0.3s ease-out;
}

@keyframes card-drop {
  0% {
    transform: scale(1.05) rotate(2deg);
    opacity: 0.9;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

/* Markdown prose styles for cards */
.task-card .prose {
  @apply text-gray-600;
}

.task-card .prose p:last-child {
  @apply mb-0;
}

.task-card .prose pre {
  @apply bg-gray-50 text-xs p-2 rounded border overflow-x-auto;
}

.task-card .prose code {
  @apply bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-xs font-mono;
}

.task-card .prose blockquote {
  @apply border-l-2 border-blue-200 pl-3 italic text-gray-700;
}

.task-card .prose ul, .task-card .prose ol {
  @apply text-xs;
}

.task-card .prose h1, .task-card .prose h2, .task-card .prose h3 {
  @apply text-gray-800;
}

.task-card .prose a {
  @apply text-blue-600 hover:text-blue-800 no-underline hover:underline;
}
