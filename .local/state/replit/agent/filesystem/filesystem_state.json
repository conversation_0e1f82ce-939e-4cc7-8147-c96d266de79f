{"file_contents": {"CLAUDE-DESKTOP-SETUP.md": {"content": "# Claude Desktop MCP Setup for Kanban Board\n\nThis guide will help you connect <PERSON> to your Kanban board using the integrated MCP server.\n\n## Prerequisites\n\n1. **Claude Des<PERSON>op** installed on your computer\n2. **Kanban board deployed** at: `https://kanban-flow-boneil.replit.app`\n\n## Step 1: Locate Claude Desktop Configuration\n\nFind your Claude Desktop configuration file:\n\n**macOS:**\n```\n~/Library/Application Support/Claude/claude_desktop_config.json\n```\n\n**Windows:**\n```\n%APPDATA%\\Claude\\claude_desktop_config.json\n```\n\n**Linux:**\n```\n~/.config/Claude/claude_desktop_config.json\n```\n\n## Step 2: Add Kanban MCP Server\n\nOpen the configuration file and add the Kanban MCP server. \n\n**Method 1: Direct HTTP Configuration (Recommended)**\n```json\n{\n  \"mcpServers\": {\n    \"kanban-board\": {\n      \"command\": \"node\",\n      \"args\": [\"-e\", \"console.log('Kanban MCP Server'); process.stdin.pause();\"],\n      \"transport\": {\n        \"type\": \"http\",\n        \"url\": \"https://kanban-flow-boneil.replit.app/mcp\"\n      }\n    }\n  }\n}\n```\n\n**Method 2: Alternative HTTP Configuration**\n```json\n{\n  \"mcpServers\": {\n    \"kanban-board\": {\n      \"command\": \"node\",\n      \"args\": [\"-e\", \"console.log('Starting HTTP transport')\"],\n      \"env\": {\n        \"MCP_SERVER_URL\": \"https://kanban-flow-boneil.replit.app/mcp\"\n      },\n      \"transport\": {\n        \"type\": \"http\",\n        \"url\": \"https://kanban-flow-boneil.replit.app/mcp\"\n      }\n    }\n  }\n}\n```\n\n**Method 3: Local Proxy (Most Compatible)**\nDownload the `claude-mcp-proxy.js` file from your project and use:\n```json\n{\n  \"mcpServers\": {\n    \"kanban-board\": {\n      \"command\": \"node\",\n      \"args\": [\"/path/to/claude-mcp-proxy.js\"]\n    }\n  }\n}\n```\n\n**Method 4: Simple Configuration (if supported)**\n```json\n{\n  \"mcpServers\": {\n    \"kanban-board\": {\n      \"url\": \"https://kanban-flow-boneil.replit.app/mcp\",\n      \"type\": \"http\"\n    }\n  }\n}\n```\n\n## Step 3: Restart Claude Desktop\n\n1. Close Claude Desktop completely\n2. Reopen Claude Desktop\n3. The Kanban tools should now be available\n\n## Step 4: Verify Connection\n\nIn Claude Desktop, try asking:\n\n> \"What tools do you have available for the kanban board?\"\n\nYou should see 8 available tools:\n- `get_cards` - Get all cards or filter by status\n- `get_cards_by_status` - Get cards grouped by status  \n- `get_card` - Get details of a specific card\n- `create_card` - Create a new card\n- `move_card` - Move a card to different status\n- `update_card` - Update card properties\n- `delete_card` - Delete a card\n- `batch_move_cards` - Move multiple cards at once\n\n## Example Commands\n\nTry these commands in Claude Desktop:\n\n**View all cards:**\n> \"Show me all the cards on the kanban board\"\n\n**Create a new card:**\n> \"Create a new card called 'Test Feature' with description 'Testing MCP integration' in the not-started column\"\n\n**Move a card:**\n> \"Move the 'Test Feature' card to in-progress status\"\n\n**Get cards by status:**\n> \"Show me all cards grouped by their status\"\n\n## Troubleshooting\n\n### Issue: Claude says no tools are available\n\n**Solution 1: Use the direct HTTP transport configuration (Most Reliable)**\nTry this configuration which connects directly to your MCP server:\n```json\n{\n  \"mcpServers\": {\n    \"kanban-board\": {\n      \"command\": \"node\",\n      \"args\": [\"-e\", \"console.log('Kanban MCP Server'); process.stdin.pause();\"],\n      \"transport\": {\n        \"type\": \"http\",\n        \"url\": \"https://kanban-flow-boneil.replit.app/mcp\"\n      }\n    }\n  }\n}\n```\n\n**Solution 2: Alternative transport configuration**\nIf the above doesn't work, try the transport format:\n```json\n{\n  \"mcpServers\": {\n    \"kanban-board\": {\n      \"command\": \"node\",\n      \"args\": [\"-e\", \"console.log('HTTP transport')\"],\n      \"transport\": {\n        \"type\": \"http\", \n        \"url\": \"https://kanban-flow-boneil.replit.app/mcp\"\n      }\n    }\n  }\n}\n```\n\n**Solution 2: Check configuration file**\n- Ensure the JSON syntax is correct (no trailing commas)\n- Verify the URL is exactly: `https://kanban-flow-boneil.replit.app/mcp`\n- Make sure the file is saved properly\n\n**Solution 3: Restart Claude Desktop properly**\n- Completely quit Claude Desktop (check Activity Monitor/Task Manager)\n- Wait 10 seconds\n- Reopen Claude Desktop\n- Wait for it to fully load before testing\n\n**Solution 4: Test the MCP endpoint manually**\nRun this in terminal/command prompt:\n```bash\ncurl -X POST https://kanban-flow-boneil.replit.app/mcp \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\"jsonrpc\": \"2.0\", \"id\": 1, \"method\": \"initialize\", \"params\": {\"protocolVersion\": \"2024-11-05\", \"capabilities\": {}, \"clientInfo\": {\"name\": \"test\", \"version\": \"1.0.0\"}}}'\n```\n\nYou should see a JSON response with server info.\n\n### Issue: Connection timeout or network error\n\n**Check:**\n- Your internet connection\n- That the Kanban app is deployed and accessible\n- Try visiting: https://kanban-flow-boneil.replit.app/mcp/health\n\n### Issue: Tools appear but don't work\n\n**Try:**\n- Ask Claude to use a specific tool: \"Use the get_cards tool to show me all cards\"\n- Check if there are any error messages in Claude's responses\n\n## Success! \n\nOnce connected, you can:\n- Ask Claude to manage your Kanban board naturally\n- Create, move, and update cards through conversation\n- Get board status and analytics\n- Perform batch operations efficiently\n\nThe MCP integration makes Claude a powerful assistant for project management through your Kanban board!", "size_bytes": 5364}, "README-MCP.md": {"content": "# Kanban MCP Server\n\nThis Model Context Protocol (MCP) server provides AI agents with tools to interact with the Kanban board API. It comes in three versions:\n\n1. **Integrated MCP Server** - Built into the main Kanban app (recommended for production)\n2. **Local MCP Server** (`mcp-server.ts`) - Uses stdio transport for local Claude Desktop integration  \n3. **Remote MCP Server** (`mcp-remote-server.ts`) - Standalone HTTP-based server\n\n## Features\n\nThe MCP server exposes the following tools for AI agents:\n\n### Card Retrieval\n- **`get_cards`** - Get all cards or filter by status\n- **`get_cards_by_status`** - Get cards grouped by status with proper ordering\n- **`get_card`** - Get details of a specific card by ID\n\n### Card Management\n- **`create_card`** - Create a new card with title, description, link, and status\n- **`update_card`** - Update properties of an existing card\n- **`delete_card`** - Delete a card from the board\n\n### Card Movement\n- **`move_card`** - Move a card to a different status and position\n- **`batch_move_cards`** - Move multiple cards in a single operation\n\n## Setup\n\n### Prerequisites\n- Node.js 18+ \n- The Kanban board server running (default: http://localhost:5000)\n\n### Installation\n\n1. Install dependencies (already done if you're in the project):\n```bash\nnpm install @modelcontextprotocol/sdk\n```\n\n2. Make the MCP server executable:\n```bash\nchmod +x mcp-server.ts\n```\n\n### Running the MCP Server\n\n#### Integrated MCP Server (Recommended)\n\nThe MCP server is now built directly into the main Kanban application for seamless deployment:\n\n**Development Mode:**\n```bash\nnpm run dev\n```\n\n**Production Mode:**\n```bash\nnpm run build\nnpm start\n```\n\n**MCP Endpoints (same host as main app):**\n- Health check: `http://your-app.com:5000/mcp/health`\n- Server info: `http://your-app.com:5000/mcp/info`\n- MCP endpoint: `http://your-app.com:5000/mcp`\n\n**Testing:**\n```bash\ntsx test-mcp-integrated.ts\n```\n\n#### Local MCP Server (stdio transport)\n\n**Development Mode:**\n```bash\ntsx mcp-server.ts\n```\n\n**Production Mode:**\n```bash\n# Build first\nesbuild mcp-server.ts --platform=node --packages=external --bundle --format=esm --outfile=dist/mcp-server.js\n\n# Run\nnode dist/mcp-server.js\n```\n\n#### Remote MCP Server (HTTP transport)\n\n**Development Mode:**\n```bash\ntsx mcp-remote-server.ts\n```\n\n**Production Mode:**\n```bash\n# Build first\nesbuild mcp-remote-server.ts --platform=node --packages=external --bundle --format=esm --outfile=dist/mcp-remote-server.js\n\n# Run\nnode dist/mcp-remote-server.js\n```\n\n**Environment Variables:**\n```bash\n# Port for the MCP remote server (default: 3001)\nexport MCP_PORT=3001\n\n# Kanban server URL (default: http://localhost:5000)\nexport KANBAN_SERVER_URL=http://your-kanban-server.com:5000\n\n# Start the remote server\ntsx mcp-remote-server.ts\n```\n\n**Remote Server Endpoints:**\n- Health check: `http://localhost:3001/health`\n- Server info: `http://localhost:3001/info`\n- MCP endpoint: `http://localhost:3001/mcp`\n\n## Client Configuration\n\n### Local MCP Server (Claude Desktop)\n\nAdd this to your Claude Desktop configuration file (`~/Library/Application Support/Claude/claude_desktop_config.json` on macOS):\n\n```json\n{\n  \"mcpServers\": {\n    \"kanban\": {\n      \"command\": \"tsx\",\n      \"args\": [\"/path/to/your/project/mcp-server.ts\"],\n      \"env\": {\n        \"KANBAN_SERVER_URL\": \"http://localhost:5000\"\n      }\n    }\n  }\n}\n```\n\n### Integrated MCP Server\n\nFor production deployments, use the integrated MCP server that runs with your main app:\n\n**MCP Client Configuration:**\n```json\n{\n  \"mcpServers\": {\n    \"kanban\": {\n      \"url\": \"http://your-kanban-app.com:5000/mcp\",\n      \"type\": \"http\"\n    }\n  }\n}\n```\n\n**Testing the Integrated Server:**\n```bash\n# Test all endpoints and tools (main app must be running)\ntsx test-mcp-integrated.ts\n\n# Or test specific endpoints\ncurl http://localhost:5000/mcp/health\ncurl http://localhost:5000/mcp/info\n```\n\n### Remote MCP Server (Alternative)\n\nFor separate deployment, use the standalone HTTP-based remote server:\n\n**MCP Client Configuration:**\n```json\n{\n  \"mcpServers\": {\n    \"kanban-remote\": {\n      \"url\": \"http://your-mcp-server.com:3001/mcp\", \n      \"type\": \"http\"\n    }\n  }\n}\n```\n\n**Testing the Remote Server:**\n```bash\n# Test all endpoints and tools\ntsx test-mcp-remote.ts\n\n# Or test specific endpoint\ncurl http://localhost:3001/health\ncurl http://localhost:3001/info\n```\n\n### Other MCP Clients\n\n- **Integrated (HTTP):** Connect to `http://your-app:5000/mcp` using HTTP transport (recommended)\n- **Local (stdio):** `tsx /path/to/your/project/mcp-server.ts`  \n- **Remote (HTTP):** Connect to `http://your-server:3001/mcp` using HTTP transport\n\n## Tool Examples\n\n### Get All Cards\n```typescript\n// Tool: get_cards\n// Arguments: {}\n// Returns: Array of all cards\n```\n\n### Filter Cards by Status\n```typescript\n// Tool: get_cards\n// Arguments: { \"status\": \"in-progress\" }\n// Returns: Array of cards with in-progress status\n```\n\n### Create a New Card\n```typescript\n// Tool: create_card\n// Arguments: {\n//   \"title\": \"New Feature\",\n//   \"description\": \"Implement new feature X\",\n//   \"link\": \"https://github.com/repo/issues/123\",\n//   \"status\": \"not-started\"\n// }\n```\n\n### Move a Card\n```typescript\n// Tool: move_card\n// Arguments: {\n//   \"id\": \"card-123\",\n//   \"status\": \"in-progress\",\n//   \"position\": 0\n// }\n```\n\n### Batch Move Cards\n```typescript\n// Tool: batch_move_cards\n// Arguments: {\n//   \"operations\": [\n//     { \"cardId\": \"card-1\", \"status\": \"complete\" },\n//     { \"cardId\": \"card-2\", \"status\": \"in-progress\", \"position\": 0 }\n//   ]\n// }\n```\n\n## Status Values\n\nThe Kanban board supports these status values:\n- `not-started` - Tasks that haven't been started yet\n- `blocked` - Tasks that are blocked by dependencies\n- `in-progress` - Tasks currently being worked on\n- `complete` - Finished tasks awaiting verification\n- `verified` - Tasks that have been completed and verified\n\n## Error Handling\n\nThe MCP server includes comprehensive error handling:\n- Network errors are caught and reported\n- API validation errors are passed through\n- Invalid tool arguments are validated by the MCP framework\n- Detailed error messages help with debugging\n\n## Real-time Updates\n\nThe Kanban board includes WebSocket support for real-time updates. When cards are moved through the MCP server, all connected clients (including the web interface) will see the changes immediately with smooth animations.\n\n## Development\n\n### Testing the MCP Server\n\nYou can test the MCP servers using the provided test scripts:\n\n- **Integrated Server:** `tsx test-mcp-integrated.ts` (tests endpoints at localhost:5000/mcp)\n- **Remote Server:** `tsx test-mcp-remote.ts` (tests standalone server at localhost:3001/mcp)\n- **Local Server:** Integrate with MCP clients like Claude Desktop using stdio transport\n\n### Adding New Tools\n\nTo add new tools:\n\n1. Add the tool definition to the `tools` array\n2. Add a case handler in the `CallToolRequestSchema` handler\n3. Update this documentation\n\n### API Compatibility\n\nThe MCP server is built on top of the Kanban board's REST API. Any changes to the API will need corresponding updates to the MCP server.", "size_bytes": 7096}, "claude-mcp-proxy.js": {"content": "#!/usr/bin/env node\n\n/**\n * Local MCP Proxy for <PERSON>\n * \n * This script acts as a stdio MCP server that proxies requests to the HTTP MCP endpoint.\n * This solves Claude Desktop's HTTP transport compatibility issues.\n */\n\nconst SERVER_URL = 'https://kanban-flow-boneil.replit.app/mcp';\n\n// Simple stdio MCP server implementation\nprocess.stdin.setEncoding('utf8');\n\nlet buffer = '';\n\nprocess.stdin.on('data', async (chunk) => {\n  buffer += chunk;\n  \n  // Process complete JSON-RPC messages\n  const lines = buffer.split('\\n');\n  buffer = lines.pop() || ''; // Keep incomplete line in buffer\n  \n  for (const line of lines) {\n    if (line.trim()) {\n      try {\n        const request = JSON.parse(line.trim());\n        await handleRequest(request);\n      } catch (error) {\n        console.error('Error parsing request:', error.message);\n      }\n    }\n  }\n});\n\nasync function handleRequest(request) {\n  try {\n    const response = await fetch(SERVER_URL, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(request)\n    });\n    \n    const result = await response.json();\n    \n    // Send response via stdout\n    process.stdout.write(JSON.stringify(result) + '\\n');\n    \n  } catch (error) {\n    // Send error response\n    const errorResponse = {\n      jsonrpc: \"2.0\",\n      id: request.id,\n      error: {\n        code: -32603,\n        message: `Proxy error: ${error.message}`\n      }\n    };\n    \n    process.stdout.write(JSON.stringify(errorResponse) + '\\n');\n  }\n}\n\n// Handle process termination gracefully\nprocess.on('SIGINT', () => {\n  process.exit(0);\n});\n\nprocess.on('SIGTERM', () => {\n  process.exit(0);\n});\n\n// Keep the process alive\nprocess.stdin.resume();", "size_bytes": 1734}, "deploy-mcp.sh": {"content": "#!/bin/bash\n\n# Kanban MCP Remote Server Deployment Script\n# This script builds and deploys the remote MCP server for production use\n\nset -e\n\necho \"🚀 Deploying Kanban MCP Remote Server...\"\n\n# Configuration\nMCP_PORT=${MCP_PORT:-3001}\nKANBAN_SERVER_URL=${KANBAN_SERVER_URL:-\"http://localhost:5000\"}\nBUILD_DIR=\"dist\"\n\n# Create build directory\necho \"📁 Creating build directory...\"\nmkdir -p $BUILD_DIR\n\n# Build the remote MCP server\necho \"🔨 Building remote MCP server...\"\nesbuild mcp-remote-server.ts \\\n  --platform=node \\\n  --packages=external \\\n  --bundle \\\n  --format=esm \\\n  --outfile=$BUILD_DIR/mcp-remote-server.js\n\n# Create production environment file\necho \"⚙️ Creating production environment...\"\ncat > $BUILD_DIR/.env << EOF\n# Kanban MCP Remote Server Configuration\nMCP_PORT=$MCP_PORT\nKANBAN_SERVER_URL=$KANBAN_SERVER_URL\nNODE_ENV=production\nEOF\n\n# Create systemd service file (optional)\nif command -v systemctl &> /dev/null; then\n  echo \"🔧 Creating systemd service file...\"\n  cat > $BUILD_DIR/kanban-mcp.service << EOF\n[Unit]\nDescription=Kanban MCP Remote Server\nAfter=network.target\n\n[Service]\nType=simple\nUser=node\nWorkingDirectory=$(pwd)/$BUILD_DIR\nEnvironment=NODE_ENV=production\nEnvironmentFile=$(pwd)/$BUILD_DIR/.env\nExecStart=node mcp-remote-server.js\nRestart=always\nRestartSec=5\n\n[Install]\nWantedBy=multi-user.target\nEOF\nfi\n\n# Create Docker setup (optional)\necho \"🐳 Creating Docker setup...\"\ncat > $BUILD_DIR/Dockerfile << EOF\nFROM node:20-alpine\n\nWORKDIR /app\n\n# Copy built server\nCOPY mcp-remote-server.js .\nCOPY .env .\n\n# Install production dependencies\nRUN npm install express cors @modelcontextprotocol/sdk\n\n# Expose port\nEXPOSE $MCP_PORT\n\n# Health check\nHEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \\\\\n  CMD curl -f http://localhost:$MCP_PORT/health || exit 1\n\n# Start server\nCMD [\"node\", \"mcp-remote-server.js\"]\nEOF\n\ncat > $BUILD_DIR/docker-compose.yml << EOF\nversion: '3.8'\n\nservices:\n  kanban-mcp:\n    build: .\n    ports:\n      - \"$MCP_PORT:$MCP_PORT\"\n    environment:\n      - MCP_PORT=$MCP_PORT\n      - KANBAN_SERVER_URL=$KANBAN_SERVER_URL\n      - NODE_ENV=production\n    restart: unless-stopped\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:$MCP_PORT/health\"]\n      interval: 30s\n      timeout: 10s\n      retries: 3\nEOF\n\n# Create startup script\necho \"🚦 Creating startup script...\"\ncat > $BUILD_DIR/start.sh << 'EOF'\n#!/bin/bash\n\n# Load environment variables\nif [ -f .env ]; then\n  export $(cat .env | xargs)\nfi\n\necho \"🚀 Starting Kanban MCP Remote Server...\"\necho \"📡 MCP Port: $MCP_PORT\"\necho \"🎯 Kanban API: $KANBAN_SERVER_URL\"\n\nnode mcp-remote-server.js\nEOF\n\nchmod +x $BUILD_DIR/start.sh\n\n# Create test script\necho \"🧪 Creating production test script...\"\ncat > $BUILD_DIR/test.sh << EOF\n#!/bin/bash\n\n# Test the production MCP server\nMCP_URL=\"http://localhost:$MCP_PORT\"\n\necho \"🧪 Testing Kanban MCP Remote Server at \\$MCP_URL\"\n\n# Test health check\necho \"1. Health check:\"\ncurl -s \\$MCP_URL/health\n\necho -e \"\\n\\n2. MCP tools list:\"\ncurl -s -X POST \\$MCP_URL/mcp \\\\\n  -H \"Content-Type: application/json\" \\\\\n  -d '{\"jsonrpc\": \"2.0\", \"id\": 1, \"method\": \"tools/list\", \"params\": {}}' \\\\\n  | head -5\n\necho -e \"\\n\\n✅ Production server test complete!\"\nEOF\n\nchmod +x $BUILD_DIR/test.sh\n\necho \"\"\necho \"✅ Deployment preparation complete!\"\necho \"\"\necho \"📋 Next steps:\"\necho \"1. Copy the '$BUILD_DIR' directory to your production server\"\necho \"2. Install Node.js 20+ on the production server\"\necho \"3. Install dependencies: npm install express cors @modelcontextprotocol/sdk\"\necho \"4. Start the server: cd $BUILD_DIR && ./start.sh\"\necho \"\"\necho \"🔗 Server endpoints:\"\necho \"   Health: http://your-server:$MCP_PORT/health\"\necho \"   Info:   http://your-server:$MCP_PORT/info\"\necho \"   MCP:    http://your-server:$MCP_PORT/mcp\"\necho \"\"\necho \"🐳 Docker deployment:\"\necho \"   cd $BUILD_DIR && docker-compose up -d\"\necho \"\"\necho \"🔧 Systemd service:\"\necho \"   sudo cp $BUILD_DIR/kanban-mcp.service /etc/systemd/system/\"\necho \"   sudo systemctl enable kanban-mcp\"\necho \"   sudo systemctl start kanban-mcp\"", "size_bytes": 4102}, "drizzle.config.ts": {"content": "import { defineConfig } from \"drizzle-kit\";\n\nif (!process.env.DATABASE_URL) {\n  throw new Error(\"DATABASE_URL, ensure the database is provisioned\");\n}\n\nexport default defineConfig({\n  out: \"./migrations\",\n  schema: \"./shared/schema.ts\",\n  dialect: \"postgresql\",\n  dbCredentials: {\n    url: process.env.DATABASE_URL,\n  },\n});\n", "size_bytes": 325}, "mcp-remote-server.ts": {"content": "#!/usr/bin/env node\n\nimport {\n  <PERSON>l,\n  CallToolRequest,\n  CallToolResult,\n  TextContent,\n  ErrorCode,\n  McpError\n} from \"@modelcontextprotocol/sdk/types.js\";\nimport express from \"express\";\nimport cors from \"cors\";\n\n// Configuration\nconst MCP_PORT = parseInt(process.env.MCP_PORT || \"3001\");\nconst KANBAN_SERVER_URL = process.env.KANBAN_SERVER_URL || \"http://localhost:5000\";\n\n/**\n * Remote Kanban MCP Server\n * \n * Provides AI agents with HTTP-accessible tools to interact with a Kanban board API.\n * This version runs as a standalone HTTP server with REST API endpoints.\n */\n\n// Helper function to make API requests\nasync function apiRequest(method: string, endpoint: string, body?: any): Promise<any> {\n  const url = `${KANBAN_SERVER_URL}${endpoint}`;\n  \n  const options: RequestInit = {\n    method,\n    headers: {\n      'Content-Type': 'application/json',\n    },\n  };\n  \n  if (body) {\n    options.body = JSON.stringify(body);\n  }\n  \n  try {\n    const response = await fetch(url, options);\n    \n    if (!response.ok) {\n      const errorText = await response.text();\n      throw new Error(`HTTP ${response.status}: ${errorText}`);\n    }\n    \n    if (response.status === 204) {\n      return null; // No content\n    }\n    \n    return await response.json();\n  } catch (error) {\n    throw new Error(`API request failed: ${error instanceof Error ? error.message : String(error)}`);\n  }\n}\n\n// Tool definitions (same as stdio version)\nconst tools: Tool[] = [\n  {\n    name: \"get_cards\",\n    description: \"Get all cards or filter by status. Returns cards sorted by their order within each status.\",\n    inputSchema: {\n      type: \"object\",\n      properties: {\n        status: {\n          type: \"string\",\n          enum: [\"not-started\", \"blocked\", \"in-progress\", \"complete\", \"verified\"],\n          description: \"Optional: filter cards by specific status\"\n        }\n      },\n      additionalProperties: false\n    }\n  },\n  {\n    name: \"get_cards_by_status\",\n    description: \"Get all cards grouped by status with proper ordering. Returns an object with status as keys and arrays of cards as values.\",\n    inputSchema: {\n      type: \"object\",\n      properties: {},\n      additionalProperties: false\n    }\n  },\n  {\n    name: \"get_card\",\n    description: \"Get details of a specific card by its ID.\",\n    inputSchema: {\n      type: \"object\",\n      properties: {\n        id: {\n          type: \"string\",\n          description: \"The ID of the card to retrieve\"\n        }\n      },\n      required: [\"id\"],\n      additionalProperties: false\n    }\n  },\n  {\n    name: \"create_card\",\n    description: \"Create a new card in the Kanban board.\",\n    inputSchema: {\n      type: \"object\",\n      properties: {\n        title: {\n          type: \"string\",\n          description: \"The title of the card\"\n        },\n        description: {\n          type: \"string\",\n          description: \"Detailed description of the card\"\n        },\n        link: {\n          type: \"string\",\n          description: \"Optional: URL link related to the card\"\n        },\n        status: {\n          type: \"string\",\n          enum: [\"not-started\", \"blocked\", \"in-progress\", \"complete\", \"verified\"],\n          description: \"The initial status of the card\",\n          default: \"not-started\"\n        }\n      },\n      required: [\"title\", \"description\"],\n      additionalProperties: false\n    }\n  },\n  {\n    name: \"move_card\",\n    description: \"Move a card to a different status and optionally specify its position within that status.\",\n    inputSchema: {\n      type: \"object\",\n      properties: {\n        id: {\n          type: \"string\",\n          description: \"The ID of the card to move\"\n        },\n        status: {\n          type: \"string\",\n          enum: [\"not-started\", \"blocked\", \"in-progress\", \"complete\", \"verified\"],\n          description: \"The target status to move the card to\"\n        },\n        position: {\n          type: \"number\",\n          description: \"Optional: position within the target status (0 = top, omit to add to end)\"\n        }\n      },\n      required: [\"id\", \"status\"],\n      additionalProperties: false\n    }\n  },\n  {\n    name: \"update_card\",\n    description: \"Update properties of an existing card.\",\n    inputSchema: {\n      type: \"object\",\n      properties: {\n        id: {\n          type: \"string\",\n          description: \"The ID of the card to update\"\n        },\n        title: {\n          type: \"string\",\n          description: \"Optional: new title for the card\"\n        },\n        description: {\n          type: \"string\",\n          description: \"Optional: new description for the card\"\n        },\n        link: {\n          type: \"string\",\n          description: \"Optional: new link for the card\"\n        },\n        status: {\n          type: \"string\",\n          enum: [\"not-started\", \"blocked\", \"in-progress\", \"complete\", \"verified\"],\n          description: \"Optional: new status for the card\"\n        }\n      },\n      required: [\"id\"],\n      additionalProperties: false\n    }\n  },\n  {\n    name: \"delete_card\",\n    description: \"Delete a card from the Kanban board.\",\n    inputSchema: {\n      type: \"object\",\n      properties: {\n        id: {\n          type: \"string\",\n          description: \"The ID of the card to delete\"\n        }\n      },\n      required: [\"id\"],\n      additionalProperties: false\n    }\n  },\n  {\n    name: \"batch_move_cards\",\n    description: \"Move multiple cards in a single operation for better performance.\",\n    inputSchema: {\n      type: \"object\",\n      properties: {\n        operations: {\n          type: \"array\",\n          items: {\n            type: \"object\",\n            properties: {\n              cardId: {\n                type: \"string\",\n                description: \"The ID of the card to move\"\n              },\n              status: {\n                type: \"string\",\n                enum: [\"not-started\", \"blocked\", \"in-progress\", \"complete\", \"verified\"],\n                description: \"The target status\"\n              },\n              position: {\n                type: \"number\",\n                description: \"Optional: position within the target status\"\n              }\n            },\n            required: [\"cardId\", \"status\"],\n            additionalProperties: false\n          },\n          description: \"Array of move operations to perform\"\n        }\n      },\n      required: [\"operations\"],\n      additionalProperties: false\n    }\n  }\n];\n\n// Tool execution function\nasync function executeTool(name: string, args: any): Promise<CallToolResult> {\n  try {\n    switch (name) {\n      case \"get_cards\": {\n        const { status } = args as { status?: string };\n        const endpoint = status ? `/api/cards?status=${encodeURIComponent(status)}` : \"/api/cards\";\n        const cards = await apiRequest(\"GET\", endpoint);\n        \n        return {\n          content: [\n            {\n              type: \"text\",\n              text: JSON.stringify(cards, null, 2)\n            } as TextContent\n          ]\n        };\n      }\n      \n      case \"get_cards_by_status\": {\n        const grouped = await apiRequest(\"GET\", \"/api/cards/by-status\");\n        \n        return {\n          content: [\n            {\n              type: \"text\",\n              text: JSON.stringify(grouped, null, 2)\n            } as TextContent\n          ]\n        };\n      }\n      \n      case \"get_card\": {\n        const { id } = args as { id: string };\n        const card = await apiRequest(\"GET\", `/api/cards/${encodeURIComponent(id)}`);\n        \n        return {\n          content: [\n            {\n              type: \"text\",\n              text: JSON.stringify(card, null, 2)\n            } as TextContent\n          ]\n        };\n      }\n      \n      case \"create_card\": {\n        const { title, description, link, status = \"not-started\" } = args as {\n          title: string;\n          description: string;\n          link?: string;\n          status?: string;\n        };\n        \n        const cardData = { title, description, link, status };\n        const card = await apiRequest(\"POST\", \"/api/cards\", cardData);\n        \n        return {\n          content: [\n            {\n              type: \"text\",\n              text: `Card created successfully:\\n${JSON.stringify(card, null, 2)}`\n            } as TextContent\n          ]\n        };\n      }\n      \n      case \"move_card\": {\n        const { id, status, position } = args as {\n          id: string;\n          status: string;\n          position?: number;\n        };\n        \n        const moveData = { status, position };\n        const card = await apiRequest(\"POST\", `/api/cards/${encodeURIComponent(id)}/move`, moveData);\n        \n        return {\n          content: [\n            {\n              type: \"text\",\n              text: `Card moved successfully:\\n${JSON.stringify(card, null, 2)}`\n            } as TextContent\n          ]\n        };\n      }\n      \n      case \"update_card\": {\n        const { id, ...updates } = args as {\n          id: string;\n          title?: string;\n          description?: string;\n          link?: string;\n          status?: string;\n        };\n        \n        const card = await apiRequest(\"PATCH\", `/api/cards/${encodeURIComponent(id)}`, updates);\n        \n        return {\n          content: [\n            {\n              type: \"text\",\n              text: `Card updated successfully:\\n${JSON.stringify(card, null, 2)}`\n            } as TextContent\n          ]\n        };\n      }\n      \n      case \"delete_card\": {\n        const { id } = args as { id: string };\n        await apiRequest(\"DELETE\", `/api/cards/${encodeURIComponent(id)}`);\n        \n        return {\n          content: [\n            {\n              type: \"text\",\n              text: `Card ${id} deleted successfully`\n            } as TextContent\n          ]\n        };\n      }\n      \n      case \"batch_move_cards\": {\n        const { operations } = args as { operations: Array<{ cardId: string; status: string; position?: number }> };\n        const result = await apiRequest(\"POST\", \"/api/cards/batch-move\", { operations });\n        \n        return {\n          content: [\n            {\n              type: \"text\",\n              text: `Batch move completed:\\n${JSON.stringify(result, null, 2)}`\n            } as TextContent\n          ]\n        };\n      }\n      \n      default:\n        throw new Error(`Tool ${name} not found`);\n    }\n  } catch (error) {\n    const errorMessage = error instanceof Error ? error.message : String(error);\n    return {\n      content: [\n        {\n          type: \"text\",\n          text: `Error: ${errorMessage}`\n        } as TextContent\n      ],\n      isError: true\n    };\n  }\n}\n\n// Create Express app for the MCP server\nconst app = express();\n\n// Enable CORS for cross-origin requests\napp.use(cors({\n  origin: true,\n  credentials: true\n}));\n\napp.use(express.json());\n\n// Health check endpoint\napp.get('/health', (req, res) => {\n  res.json({ \n    status: 'healthy', \n    mcpServer: 'kanban-remote-server',\n    version: '1.0.0',\n    kanbanServerUrl: KANBAN_SERVER_URL,\n    timestamp: new Date().toISOString()\n  });\n});\n\n// Info endpoint\napp.get('/info', (req, res) => {\n  res.json({\n    name: 'Kanban MCP Remote Server',\n    version: '1.0.0',\n    description: 'Model Context Protocol server for Kanban board management',\n    tools: tools.map(tool => ({\n      name: tool.name,\n      description: tool.description\n    })),\n    endpoints: {\n      health: '/health',\n      info: '/info',\n      mcp: '/sse'\n    },\n    configuration: {\n      mcpPort: MCP_PORT,\n      kanbanServerUrl: KANBAN_SERVER_URL\n    }\n  });\n});\n\n// MCP Protocol endpoints\napp.post('/mcp', async (req, res) => {\n  try {\n    const { jsonrpc, id, method, params } = req.body;\n\n    if (jsonrpc !== \"2.0\") {\n      return res.status(400).json({\n        jsonrpc: \"2.0\",\n        id,\n        error: { code: -32600, message: \"Invalid Request\" }\n      });\n    }\n\n    switch (method) {\n      case \"tools/list\":\n        res.json({\n          jsonrpc: \"2.0\",\n          id,\n          result: { tools }\n        });\n        break;\n\n      case \"tools/call\":\n        const { name, arguments: args } = params;\n        const result = await executeTool(name, args || {});\n        res.json({\n          jsonrpc: \"2.0\",\n          id,\n          result\n        });\n        break;\n\n      default:\n        res.status(404).json({\n          jsonrpc: \"2.0\",\n          id,\n          error: { code: -32601, message: \"Method not found\" }\n        });\n    }\n  } catch (error) {\n    const errorMessage = error instanceof Error ? error.message : String(error);\n    res.status(500).json({\n      jsonrpc: \"2.0\",\n      id: req.body.id,\n      error: { code: -32603, message: `Internal error: ${errorMessage}` }\n    });\n  }\n});\n\n// Start the server\nasync function main() {\n  // Start the HTTP server\n  app.listen(MCP_PORT, '0.0.0.0', () => {\n    console.log(`🚀 Kanban MCP Remote Server running on port ${MCP_PORT}`);\n    console.log(`📊 Health check: http://localhost:${MCP_PORT}/health`);\n    console.log(`📋 Info endpoint: http://localhost:${MCP_PORT}/info`);\n    console.log(`🔗 MCP endpoint: http://localhost:${MCP_PORT}/mcp`);\n    console.log(`🎯 Kanban API: ${KANBAN_SERVER_URL}`);\n  });\n}\n\n// Handle graceful shutdown\nprocess.on('SIGINT', () => {\n  console.log('\\n🛑 Shutting down Kanban MCP Remote Server...');\n  process.exit(0);\n});\n\nprocess.on('SIGTERM', () => {\n  console.log('\\n🛑 Shutting down Kanban MCP Remote Server...');\n  process.exit(0);\n});\n\nmain().catch((error) => {\n  console.error(\"❌ Server failed to start:\", error);\n  process.exit(1);\n});", "size_bytes": 13447}, "mcp-server.ts": {"content": "#!/usr/bin/env node\n\nimport { Server } from \"@modelcontextprotocol/sdk/server/index.js\";\nimport { StdioServerTransport } from \"@modelcontextprotocol/sdk/server/stdio.js\";\nimport {\n  CallToolRequestSchema,\n  ListToolsRequestSchema,\n  Tool,\n  CallToolRequest,\n  CallToolResult,\n  TextContent,\n  ImageContent,\n  EmbeddedResource,\n  ErrorCode,\n  McpError\n} from \"@modelcontextprotocol/sdk/types.js\";\n\n// Default server URL - can be overridden with environment variable\nconst KANBAN_SERVER_URL = process.env.KANBAN_SERVER_URL || \"http://localhost:5000\";\n\n/**\n * Kanban MCP Server\n * \n * Provides AI agents with tools to interact with a Kanban board API:\n * - Get all cards or filter by status\n * - Get cards grouped by status\n * - Get specific card details\n * - Create new cards\n * - Move cards between statuses and positions\n * - Update card properties\n * - Delete cards\n * - Batch move operations\n */\n\nconst server = new Server(\n  {\n    name: \"kanban-server\",\n    version: \"1.0.0\",\n  },\n  {\n    capabilities: {\n      tools: {},\n    },\n  }\n);\n\n// Helper function to make API requests\nasync function apiRequest(method: string, endpoint: string, body?: any): Promise<any> {\n  const url = `${KANBAN_SERVER_URL}${endpoint}`;\n  \n  const options: RequestInit = {\n    method,\n    headers: {\n      'Content-Type': 'application/json',\n    },\n  };\n  \n  if (body) {\n    options.body = JSON.stringify(body);\n  }\n  \n  try {\n    const response = await fetch(url, options);\n    \n    if (!response.ok) {\n      const errorText = await response.text();\n      throw new Error(`HTTP ${response.status}: ${errorText}`);\n    }\n    \n    if (response.status === 204) {\n      return null; // No content\n    }\n    \n    return await response.json();\n  } catch (error) {\n    throw new Error(`API request failed: ${error instanceof Error ? error.message : String(error)}`);\n  }\n}\n\n// Tool definitions\nconst tools: Tool[] = [\n  {\n    name: \"get_cards\",\n    description: \"Get all cards or filter by status. Returns cards sorted by their order within each status.\",\n    inputSchema: {\n      type: \"object\",\n      properties: {\n        status: {\n          type: \"string\",\n          enum: [\"not-started\", \"blocked\", \"in-progress\", \"complete\", \"verified\"],\n          description: \"Optional: filter cards by specific status\"\n        }\n      },\n      additionalProperties: false\n    }\n  },\n  {\n    name: \"get_cards_by_status\",\n    description: \"Get all cards grouped by status with proper ordering. Returns an object with status as keys and arrays of cards as values.\",\n    inputSchema: {\n      type: \"object\",\n      properties: {},\n      additionalProperties: false\n    }\n  },\n  {\n    name: \"get_card\",\n    description: \"Get details of a specific card by its ID.\",\n    inputSchema: {\n      type: \"object\",\n      properties: {\n        id: {\n          type: \"string\",\n          description: \"The ID of the card to retrieve\"\n        }\n      },\n      required: [\"id\"],\n      additionalProperties: false\n    }\n  },\n  {\n    name: \"create_card\",\n    description: \"Create a new card in the Kanban board.\",\n    inputSchema: {\n      type: \"object\",\n      properties: {\n        title: {\n          type: \"string\",\n          description: \"The title of the card\"\n        },\n        description: {\n          type: \"string\",\n          description: \"Detailed description of the card\"\n        },\n        link: {\n          type: \"string\",\n          description: \"Optional: URL link related to the card\"\n        },\n        status: {\n          type: \"string\",\n          enum: [\"not-started\", \"blocked\", \"in-progress\", \"complete\", \"verified\"],\n          description: \"The initial status of the card\",\n          default: \"not-started\"\n        }\n      },\n      required: [\"title\", \"description\"],\n      additionalProperties: false\n    }\n  },\n  {\n    name: \"move_card\",\n    description: \"Move a card to a different status and optionally specify its position within that status.\",\n    inputSchema: {\n      type: \"object\",\n      properties: {\n        id: {\n          type: \"string\",\n          description: \"The ID of the card to move\"\n        },\n        status: {\n          type: \"string\",\n          enum: [\"not-started\", \"blocked\", \"in-progress\", \"complete\", \"verified\"],\n          description: \"The target status to move the card to\"\n        },\n        position: {\n          type: \"number\",\n          description: \"Optional: position within the target status (0 = top, omit to add to end)\"\n        }\n      },\n      required: [\"id\", \"status\"],\n      additionalProperties: false\n    }\n  },\n  {\n    name: \"update_card\",\n    description: \"Update properties of an existing card.\",\n    inputSchema: {\n      type: \"object\",\n      properties: {\n        id: {\n          type: \"string\",\n          description: \"The ID of the card to update\"\n        },\n        title: {\n          type: \"string\",\n          description: \"Optional: new title for the card\"\n        },\n        description: {\n          type: \"string\",\n          description: \"Optional: new description for the card\"\n        },\n        link: {\n          type: \"string\",\n          description: \"Optional: new link for the card\"\n        },\n        status: {\n          type: \"string\",\n          enum: [\"not-started\", \"blocked\", \"in-progress\", \"complete\", \"verified\"],\n          description: \"Optional: new status for the card\"\n        }\n      },\n      required: [\"id\"],\n      additionalProperties: false\n    }\n  },\n  {\n    name: \"delete_card\",\n    description: \"Delete a card from the Kanban board.\",\n    inputSchema: {\n      type: \"object\",\n      properties: {\n        id: {\n          type: \"string\",\n          description: \"The ID of the card to delete\"\n        }\n      },\n      required: [\"id\"],\n      additionalProperties: false\n    }\n  },\n  {\n    name: \"batch_move_cards\",\n    description: \"Move multiple cards in a single operation for better performance.\",\n    inputSchema: {\n      type: \"object\",\n      properties: {\n        operations: {\n          type: \"array\",\n          items: {\n            type: \"object\",\n            properties: {\n              cardId: {\n                type: \"string\",\n                description: \"The ID of the card to move\"\n              },\n              status: {\n                type: \"string\",\n                enum: [\"not-started\", \"blocked\", \"in-progress\", \"complete\", \"verified\"],\n                description: \"The target status\"\n              },\n              position: {\n                type: \"number\",\n                description: \"Optional: position within the target status\"\n              }\n            },\n            required: [\"cardId\", \"status\"],\n            additionalProperties: false\n          },\n          description: \"Array of move operations to perform\"\n        }\n      },\n      required: [\"operations\"],\n      additionalProperties: false\n    }\n  }\n];\n\n// Register tool handlers\nserver.setRequestHandler(ListToolsRequestSchema, async () => {\n  return { tools };\n});\n\nserver.setRequestHandler(CallToolRequestSchema, async (request: CallToolRequest): Promise<CallToolResult> => {\n  const { name, arguments: args } = request.params;\n  \n  try {\n    switch (name) {\n      case \"get_cards\": {\n        const { status } = args as { status?: string };\n        const endpoint = status ? `/api/cards?status=${encodeURIComponent(status)}` : \"/api/cards\";\n        const cards = await apiRequest(\"GET\", endpoint);\n        \n        return {\n          content: [\n            {\n              type: \"text\",\n              text: JSON.stringify(cards, null, 2)\n            } as TextContent\n          ]\n        };\n      }\n      \n      case \"get_cards_by_status\": {\n        const grouped = await apiRequest(\"GET\", \"/api/cards/by-status\");\n        \n        return {\n          content: [\n            {\n              type: \"text\",\n              text: JSON.stringify(grouped, null, 2)\n            } as TextContent\n          ]\n        };\n      }\n      \n      case \"get_card\": {\n        const { id } = args as { id: string };\n        const card = await apiRequest(\"GET\", `/api/cards/${encodeURIComponent(id)}`);\n        \n        return {\n          content: [\n            {\n              type: \"text\",\n              text: JSON.stringify(card, null, 2)\n            } as TextContent\n          ]\n        };\n      }\n      \n      case \"create_card\": {\n        const { title, description, link, status = \"not-started\" } = args as {\n          title: string;\n          description: string;\n          link?: string;\n          status?: string;\n        };\n        \n        const cardData = { title, description, link, status };\n        const card = await apiRequest(\"POST\", \"/api/cards\", cardData);\n        \n        return {\n          content: [\n            {\n              type: \"text\",\n              text: `Card created successfully:\\n${JSON.stringify(card, null, 2)}`\n            } as TextContent\n          ]\n        };\n      }\n      \n      case \"move_card\": {\n        const { id, status, position } = args as {\n          id: string;\n          status: string;\n          position?: number;\n        };\n        \n        const moveData = { status, position };\n        const card = await apiRequest(\"POST\", `/api/cards/${encodeURIComponent(id)}/move`, moveData);\n        \n        return {\n          content: [\n            {\n              type: \"text\",\n              text: `Card moved successfully:\\n${JSON.stringify(card, null, 2)}`\n            } as TextContent\n          ]\n        };\n      }\n      \n      case \"update_card\": {\n        const { id, ...updates } = args as {\n          id: string;\n          title?: string;\n          description?: string;\n          link?: string;\n          status?: string;\n        };\n        \n        const card = await apiRequest(\"PATCH\", `/api/cards/${encodeURIComponent(id)}`, updates);\n        \n        return {\n          content: [\n            {\n              type: \"text\",\n              text: `Card updated successfully:\\n${JSON.stringify(card, null, 2)}`\n            } as TextContent\n          ]\n        };\n      }\n      \n      case \"delete_card\": {\n        const { id } = args as { id: string };\n        await apiRequest(\"DELETE\", `/api/cards/${encodeURIComponent(id)}`);\n        \n        return {\n          content: [\n            {\n              type: \"text\",\n              text: `Card ${id} deleted successfully`\n            } as TextContent\n          ]\n        };\n      }\n      \n      case \"batch_move_cards\": {\n        const { operations } = args as { operations: Array<{ cardId: string; status: string; position?: number }> };\n        const result = await apiRequest(\"POST\", \"/api/cards/batch-move\", { operations });\n        \n        return {\n          content: [\n            {\n              type: \"text\",\n              text: `Batch move completed:\\n${JSON.stringify(result, null, 2)}`\n            } as TextContent\n          ]\n        };\n      }\n      \n      default:\n        throw new McpError(ErrorCode.MethodNotFound, `Tool ${name} not found`);\n    }\n  } catch (error) {\n    const errorMessage = error instanceof Error ? error.message : String(error);\n    return {\n      content: [\n        {\n          type: \"text\",\n          text: `Error: ${errorMessage}`\n        } as TextContent\n      ],\n      isError: true\n    };\n  }\n});\n\n// Start the server\nasync function main() {\n  const transport = new StdioServerTransport();\n  await server.connect(transport);\n  console.error(\"Kanban MCP Server running on stdio\");\n}\n\nmain().catch((error) => {\n  console.error(\"Server failed to start:\", error);\n  process.exit(1);\n});", "size_bytes": 11453}, "postcss.config.js": {"content": "export default {\n  plugins: {\n    tailwindcss: {},\n    autoprefixer: {},\n  },\n}\n", "size_bytes": 80}, "replit.md": {"content": "# Kanban Board Application\n\n## Overview\n\nThis is a full-stack Kanban board application built with React (TypeScript) on the frontend and Express.js on the backend. The application allows users to manage tasks through a smooth drag-and-drop interface with different status columns (not-started, blocked, in-progress, complete, verified). Features advanced drag-and-drop with card insertion, reordering, and optimistic updates for seamless user experience.\n\n## User Preferences\n\nPreferred communication style: Simple, everyday language.\n\n## System Architecture\n\n### Frontend Architecture\n- **Framework**: React with TypeScript and Vite for development/build tooling\n- **UI Library**: shadcn/ui components built on Radix UI primitives\n- **Styling**: Tailwind CSS with CSS variables for theming\n- **State Management**: TanStack Query (React Query) for server state management\n- **Form Handling**: React Hook Form with Zod validation\n- **Drag & Drop**: @dnd-kit library with Framer Motion for smooth animations\n- **Real-time**: WebSocket client with automatic reconnection\n- **Routing**: Wouter for lightweight client-side routing\n\n### Backend Architecture\n- **Framework**: Express.js with TypeScript\n- **Database**: PostgreSQL with Drizzle ORM (migrated from in-memory storage)\n- **Database Provider**: Neon Database (serverless PostgreSQL)\n- **API Style**: RESTful API endpoints with comprehensive CRUD operations\n- **Real-time**: WebSocket server broadcasting live updates\n- **Production-Ready**: Full database persistence with proper schema management\n\n### MCP Integration\n- **Integrated MCP Server**: Model Context Protocol endpoints built into main application (recommended)\n- **Local MCP Server**: stdio transport version for Claude Desktop integration  \n- **Remote MCP Server**: Standalone HTTP-based server for separate deployment\n- **SDK**: @modelcontextprotocol/sdk for TypeScript implementation\n- **Tools**: 8 comprehensive tools for card management and movement\n- **Endpoints**: `/mcp/health`, `/mcp/info`, `/mcp` (JSON-RPC 2.0 protocol)\n- **Real-time**: MCP actions trigger WebSocket broadcasts for live updates\n\n## Key Components\n\n### Database Schema\n- **Cards Table**: Stores kanban cards with id, title, description, link, and status fields\n- **Users Table**: Basic user structure (id, username, password) - appears to be prepared for future authentication\n- **Status Types**: Predefined kanban statuses (not-started, blocked, in-progress, complete, verified)\n\n### Frontend Components\n- **KanbanBoard**: Main board component managing drag-and-drop functionality\n- **KanbanColumn**: Individual columns for each status type\n- **TaskCard**: Individual card components with drag capabilities\n- **AddCardDialog**: Modal form for creating new cards\n- **UI Components**: Comprehensive shadcn/ui component library\n\n### Backend Components\n- **Storage Layer**: Abstracted storage interface with in-memory implementation\n- **Routes**: RESTful endpoints for CRUD operations on cards\n- **Validation**: Zod schemas shared between frontend and backend\n\n## Data Flow\n\n1. **Card Retrieval**: Frontend fetches cards via GET /api/cards, sorted by order within columns\n2. **Card Creation**: Forms submit to POST /api/cards with validation\n3. **Card Updates**: Drag-and-drop triggers PATCH /api/cards/:id for status/order changes\n4. **Card Movement**: POST /api/cards/:id/move endpoint with automatic position calculation\n5. **Batch Operations**: POST /api/cards/batch-move for efficient multi-card movements\n6. **Optimistic Updates**: Immediate UI updates before server confirmation for smooth UX\n7. **Real-time Updates**: WebSocket broadcasts changes to all connected clients\n8. **Animation System**: Framer Motion LayoutGroup enables smooth cross-column animations\n9. **MCP Integration**: AI agents can perform all operations via local or remote MCP tools\n10. **Remote API**: HTTP-based MCP server for production agent integration\n11. **Error Handling**: Toast notifications for user feedback\n\n## External Dependencies\n\n### Core Dependencies\n- **@neondatabase/serverless**: PostgreSQL database connection\n- **drizzle-orm**: Type-safe database ORM\n- **@tanstack/react-query**: Server state management\n- **@dnd-kit/***: Drag and drop functionality\n- **@radix-ui/***: Accessible UI primitives\n- **react-hook-form**: Form state management\n- **zod**: Runtime type validation\n\n### Development Tools\n- **Vite**: Build tool and development server\n- **TypeScript**: Type safety across the stack\n- **Tailwind CSS**: Utility-first styling\n- **ESBuild**: Production bundling\n\n## Deployment Strategy\n\nThe application is configured for deployment with:\n\n- **Build Process**: Vite builds the frontend to `dist/public`, ESBuild bundles the backend to `dist/index.js`\n- **Environment Variables**: `DATABASE_URL` required for PostgreSQL connection\n- **Production Mode**: Serves static files from Express in production\n- **Development Mode**: Vite dev server with HMR and proxy setup\n\nThe architecture supports both development (with in-memory storage) and production (with PostgreSQL) environments, with the storage layer abstracted to allow easy switching between implementations.\n\n## Recent Changes\n\n### July 30, 2025\n- **Cards Summary View**: Added floating summary component with card titles and status counts, real-time updates, and markdown export\n- **Summary API Endpoints**: GET /api/cards/summary (JSON) and /api/cards/summary/markdown for external access\n- **Real-time Summary Updates**: Summary automatically refreshes via WebSocket when cards change  \n- **Draggable Summary Card**: Made summary component draggable with move handle and improved text contrast for better readability\n- **Data Consistency Fix**: Fixed status mismatch where cards had \"completed\" status but frontend expected \"complete\"\n- **Status Validation**: Enforced valid kanban statuses (not-started, blocked, in-progress, complete, verified) in API and MCP tools\n- **Enhanced Error Messages**: Added clear validation errors when MCP agents use invalid statuses\n- **Schema Validation**: Updated Zod schemas to strictly validate status enum values\n- **Bulk Delete API & MCP Tool**: Added efficient bulk card deletion via DELETE /api/cards/bulk endpoint and bulk_delete_cards MCP tool\n- **Enhanced MCP Capabilities**: MCP now supports deleting multiple cards by ID array for better automation efficiency\n- **Real-time Bulk Updates**: Bulk deletions broadcast CARDS_BULK_DELETED events to update all connected clients\n- **Full-Screen Card View**: Added comprehensive view mode with large dialog showing complete card details in readable format\n- **Enhanced Card Actions**: Cards now show view (purple eye), edit (green pencil), copy (blue copy), and delete buttons on hover\n- **Rich Content Display**: View dialog uses proper Markdown rendering with enhanced typography and formatting\n- **Integrated Actions**: View dialog includes quick access to edit, copy, and external link functionality\n- **Card Editing**: Added comprehensive card editing functionality with dialog form supporting title, description, link, and status changes\n- **Clipboard Copy**: Added one-click copy functionality to export card content as Markdown format\n- **Visual Feedback**: Copy button shows checkmark confirmation and toast notification when successful\n- **Column Width Persistence**: Added localStorage persistence for column widths - resized columns now remember their width when you reload the page\n- **Column Width Reset**: Added reset button (rotate icon) in header to restore all columns to default 320px width\n- **Enhanced UX**: Column width changes are automatically saved and restored across browser sessions\n- **Improved Controls**: New visual indicator for column width reset functionality with toast notifications\n- **Full-Width Layout**: Updated page layout so scrollable columns area fills full page width while keeping header controls centered\n- **Collapsible Summary**: Made summary component collapsed by default with localStorage persistence for visibility and position\n- **Position Memory**: Summary component remembers its dragged position across browser sessions\n\n### July 29, 2025\n- **Enhanced Card Design**: Added expandable/collapsible card views with smooth animations\n- **Markdown Support**: Full Markdown formatting support in card descriptions using react-markdown\n- **MCP Markdown Integration**: Updated MCP tools to encourage agents to use Markdown formatting\n- **Improved Card Layout**: Cards now use proper flex layouts, titles never get cropped, and long descriptions show expand/collapse buttons\n- **Real-time Updates**: Fixed WebSocket broadcasting for database operations to ensure live UI updates\n- **Create Project UI**: Added project creation functionality with automatic URL navigation\n- **Dark Mode Implementation**: Comprehensive dark theme with theme provider, localStorage persistence, and smooth transitions\n- **Resizable Columns**: Added column resize functionality with drag handles (280px-600px range)\n- **Responsive Layout**: Horizontal scrolling container prevents column overlap on narrow screens\n- **Theme Toggle**: Positioned in top-right header with sun/moon icon animations\n\n### July 28, 2025\n- **Integrated MCP Server**: Built MCP endpoints directly into main application for single deployment\n- **Triple MCP Architecture**: Now supports integrated (recommended), local (stdio), and remote (HTTP) MCP\n- **Production Optimized**: Integrated server shares same port and infrastructure as main app\n- **Comprehensive Testing**: Added test suites for all MCP server variants\n- **Real-time MCP**: MCP operations trigger WebSocket broadcasts for live UI updates\n- **Multi-Project Support**: Added project property to cards for organizing different boards by project\n- **Project Filtering**: API endpoints now support project-based filtering with GET /api/projects\n- **Enhanced UI**: Project selector in kanban board header with formatted project names\n- **Sample Data**: Three demo projects (E-commerce Platform, Mobile App, Marketing Website)\n- **MCP Project Integration**: Updated all 9 MCP tools to support project filtering and management\n- **Enhanced MCP Tools**: New get_projects tool, project parameters in get_cards/get_cards_by_status/create_card\n- **Comprehensive Testing**: Verified project-based MCP functionality with test suite", "size_bytes": 10325}, "tailwind.config.ts": {"content": "import type { Config } from \"tailwindcss\";\n\nexport default {\n  darkMode: [\"class\"],\n  content: [\"./client/index.html\", \"./client/src/**/*.{js,jsx,ts,tsx}\"],\n  theme: {\n    extend: {\n      borderRadius: {\n        lg: \"var(--radius)\",\n        md: \"calc(var(--radius) - 2px)\",\n        sm: \"calc(var(--radius) - 4px)\",\n      },\n      colors: {\n        background: \"var(--background)\",\n        foreground: \"var(--foreground)\",\n        card: {\n          DEFAULT: \"var(--card)\",\n          foreground: \"var(--card-foreground)\",\n        },\n        popover: {\n          DEFAULT: \"var(--popover)\",\n          foreground: \"var(--popover-foreground)\",\n        },\n        primary: {\n          DEFAULT: \"var(--primary)\",\n          foreground: \"var(--primary-foreground)\",\n        },\n        secondary: {\n          DEFAULT: \"var(--secondary)\",\n          foreground: \"var(--secondary-foreground)\",\n        },\n        muted: {\n          DEFAULT: \"var(--muted)\",\n          foreground: \"var(--muted-foreground)\",\n        },\n        accent: {\n          DEFAULT: \"var(--accent)\",\n          foreground: \"var(--accent-foreground)\",\n        },\n        destructive: {\n          DEFAULT: \"var(--destructive)\",\n          foreground: \"var(--destructive-foreground)\",\n        },\n        border: \"var(--border)\",\n        input: \"var(--input)\",\n        ring: \"var(--ring)\",\n        chart: {\n          \"1\": \"var(--chart-1)\",\n          \"2\": \"var(--chart-2)\",\n          \"3\": \"var(--chart-3)\",\n          \"4\": \"var(--chart-4)\",\n          \"5\": \"var(--chart-5)\",\n        },\n        sidebar: {\n          DEFAULT: \"var(--sidebar-background)\",\n          foreground: \"var(--sidebar-foreground)\",\n          primary: \"var(--sidebar-primary)\",\n          \"primary-foreground\": \"var(--sidebar-primary-foreground)\",\n          accent: \"var(--sidebar-accent)\",\n          \"accent-foreground\": \"var(--sidebar-accent-foreground)\",\n          border: \"var(--sidebar-border)\",\n          ring: \"var(--sidebar-ring)\",\n        },\n      },\n      keyframes: {\n        \"accordion-down\": {\n          from: {\n            height: \"0\",\n          },\n          to: {\n            height: \"var(--radix-accordion-content-height)\",\n          },\n        },\n        \"accordion-up\": {\n          from: {\n            height: \"var(--radix-accordion-content-height)\",\n          },\n          to: {\n            height: \"0\",\n          },\n        },\n      },\n      animation: {\n        \"accordion-down\": \"accordion-down 0.2s ease-out\",\n        \"accordion-up\": \"accordion-up 0.2s ease-out\",\n      },\n    },\n  },\n  plugins: [require(\"tailwindcss-animate\"), require(\"@tailwindcss/typography\")],\n} satisfies Config;\n", "size_bytes": 2627}, "test-bulk-delete-mcp.ts": {"content": "// Test script for the new bulk delete MCP tool\nconst SERVER_URL = 'http://localhost:5000';\n\ninterface McpRequest {\n  jsonrpc: string;\n  id: number;\n  method: string;\n  params?: any;\n}\n\ninterface McpResponse {\n  jsonrpc: string;\n  id: number;\n  result?: any;\n  error?: any;\n}\n\n// Helper function to make MCP requests\nasync function mcpRequest(method: string, params?: any): Promise<any> {\n  const request: McpRequest = {\n    jsonrpc: \"2.0\",\n    id: Date.now(),\n    method,\n    params\n  };\n\n  const response = await fetch(`${SERVER_URL}/mcp`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(request)\n  });\n\n  if (!response.ok) {\n    throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n  }\n\n  const data: McpResponse = await response.json();\n  \n  if (data.error) {\n    throw new Error(`MCP Error: ${data.error.message}`);\n  }\n\n  return data.result;\n}\n\nasync function testListTools() {\n  console.log('🔧 Testing MCP tools list...');\n  const result = await mcpRequest('tools/list');\n  const bulkDeleteTool = result.tools.find((tool: any) => tool.name === 'bulk_delete_cards');\n  if (bulkDeleteTool) {\n    console.log('✅ Found bulk_delete_cards tool:', bulkDeleteTool.description);\n  } else {\n    console.log('❌ bulk_delete_cards tool not found');\n  }\n  return result;\n}\n\nasync function createTestCards() {\n  console.log('➕ Creating test cards for bulk deletion...');\n  const cardIds = [];\n  \n  for (let i = 1; i <= 3; i++) {\n    const result = await mcpRequest('tools/call', {\n      name: 'create_card',\n      arguments: {\n        title: `Test Card ${i} for Bulk Delete`,\n        description: `This is test card ${i} that will be deleted in bulk`,\n        project: 'Test Project',\n        status: 'not-started'\n      }\n    });\n    \n    const cardData = JSON.parse(result.content[0].text.split(':\\n')[1]);\n    cardIds.push(cardData.id);\n    console.log(`✅ Created card ${i}: ${cardData.id}`);\n  }\n  \n  return cardIds;\n}\n\nasync function testBulkDelete(cardIds: string[]) {\n  console.log('🗑️ Testing bulk delete cards...');\n  const result = await mcpRequest('tools/call', {\n    name: 'bulk_delete_cards',\n    arguments: {\n      ids: cardIds\n    }\n  });\n  \n  const response = JSON.parse(result.content[0].text.split(':\\n')[1]);\n  console.log('✅ Bulk delete result:', response);\n  \n  if (response.deletedCount === cardIds.length) {\n    console.log('✅ All cards deleted successfully');\n  } else {\n    console.log(`⚠️ Only ${response.deletedCount}/${cardIds.length} cards deleted`);\n  }\n  \n  return response;\n}\n\nasync function testBulkDeleteWithInvalidIds() {\n  console.log('🧪 Testing bulk delete with invalid IDs...');\n  const result = await mcpRequest('tools/call', {\n    name: 'bulk_delete_cards',\n    arguments: {\n      ids: ['invalid-id-1', 'invalid-id-2']\n    }\n  });\n  \n  const response = JSON.parse(result.content[0].text.split(':\\n')[1]);\n  console.log('✅ Bulk delete with invalid IDs:', response);\n  return response;\n}\n\nasync function testBulkDeleteEmptyArray() {\n  console.log('🧪 Testing bulk delete with empty array...');\n  try {\n    const result = await mcpRequest('tools/call', {\n      name: 'bulk_delete_cards',\n      arguments: {\n        ids: []\n      }\n    });\n    console.log('❌ Should have failed with empty array');\n  } catch (error) {\n    console.log('✅ Correctly rejected empty array:', error.message);\n  }\n}\n\n// Main test runner\nasync function runBulkDeleteTests() {\n  console.log(`🚀 Testing Bulk Delete MCP Tool at ${SERVER_URL}\\n`);\n  \n  try {\n    // Test tool availability\n    await testListTools();\n    console.log();\n\n    // Test bulk delete with valid cards\n    const cardIds = await createTestCards();\n    console.log();\n    \n    await testBulkDelete(cardIds);\n    console.log();\n    \n    // Test error cases\n    await testBulkDeleteWithInvalidIds();\n    console.log();\n    \n    await testBulkDeleteEmptyArray();\n    console.log();\n\n    console.log('🎉 All bulk delete tests completed!');\n    console.log('📝 The bulk_delete_cards MCP tool is working correctly.');\n\n  } catch (error) {\n    console.error('❌ Test failed:', error);\n    process.exit(1);\n  }\n}\n\n// Run tests if this file is executed directly\nif (require.main === module) {\n  runBulkDeleteTests();\n}\n\nexport { runBulkDeleteTests };", "size_bytes": 4335}, "test-mcp-integrated.ts": {"content": "#!/usr/bin/env tsx\n\n/**\n * Kanban MCP Integrated Server Test Suite\n * \n * Tests the integrated MCP server endpoints that are part of the main Kanban application.\n * This version runs on the same port as the main app (5000) under the /mcp path.\n */\n\nconst SERVER_URL = process.env.SERVER_URL || 'http://localhost:5000';\n\ninterface McpRequest {\n  jsonrpc: string;\n  id: number;\n  method: string;\n  params?: any;\n}\n\ninterface McpResponse {\n  jsonrpc: string;\n  id: number;\n  result?: any;\n  error?: any;\n}\n\n// Helper function to make MCP requests\nasync function mcpRequest(method: string, params?: any): Promise<any> {\n  const request: McpRequest = {\n    jsonrpc: \"2.0\",\n    id: Date.now(),\n    method,\n    params\n  };\n\n  const response = await fetch(`${SERVER_URL}/mcp`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(request)\n  });\n\n  if (!response.ok) {\n    throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n  }\n\n  const data: McpResponse = await response.json();\n  \n  if (data.error) {\n    throw new Error(`MCP Error: ${data.error.message}`);\n  }\n\n  return data.result;\n}\n\n// Test functions\nasync function testHealthCheck() {\n  console.log('🏥 Testing MCP health check...');\n  const response = await fetch(`${SERVER_URL}/mcp/health`);\n  const health = await response.json();\n  console.log('✅ Health check:', health.status, '- Server:', health.mcpServer);\n  return health;\n}\n\nasync function testServerInfo() {\n  console.log('📋 Testing MCP server info...');\n  const response = await fetch(`${SERVER_URL}/mcp/info`);\n  const info = await response.json();\n  console.log(`✅ Server info: ${info.name} v${info.version} with ${info.tools.length} tools`);\n  return info;\n}\n\nasync function testListTools() {\n  console.log('🔧 Testing list tools...');\n  const result = await mcpRequest('tools/list');\n  console.log(`✅ Found ${result.tools.length} tools:`);\n  result.tools.forEach((tool: any) => {\n    console.log(`   • ${tool.name}: ${tool.description}`);\n  });\n  return result.tools;\n}\n\nasync function testGetCards() {\n  console.log('📋 Testing get cards...');\n  const result = await mcpRequest('tools/call', {\n    name: 'get_cards',\n    arguments: {}\n  });\n  const cards = JSON.parse(result.content[0].text);\n  console.log(`✅ Retrieved ${cards.length} cards`);\n  return cards;\n}\n\nasync function testGetCardsByStatus() {\n  console.log('📊 Testing get cards by status...');\n  const result = await mcpRequest('tools/call', {\n    name: 'get_cards_by_status',\n    arguments: {}\n  });\n  const grouped = JSON.parse(result.content[0].text);\n  const statusCounts = Object.keys(grouped).map(status => \n    `${status}: ${grouped[status].length}`\n  ).join(', ');\n  console.log(`✅ Cards by status: ${statusCounts}`);\n  return grouped;\n}\n\nasync function testCreateCard() {\n  console.log('➕ Testing create card...');\n  const result = await mcpRequest('tools/call', {\n    name: 'create_card',\n    arguments: {\n      title: `Test Card ${Date.now()}`,\n      description: 'This is a test card created via integrated MCP',\n      status: 'not-started'\n    }\n  });\n  console.log('✅ Create card result:', result.content[0].text.split('\\n')[0]);\n  \n  // Extract card ID from response\n  const cardData = JSON.parse(result.content[0].text.split(':\\n')[1]);\n  return cardData.id;\n}\n\nasync function testMoveCard(cardId: string) {\n  console.log('🔄 Testing move card...');\n  const result = await mcpRequest('tools/call', {\n    name: 'move_card',\n    arguments: {\n      id: cardId,\n      status: 'in-progress',\n      position: 0\n    }\n  });\n  console.log('✅ Move card result:', result.content[0].text.split('\\n')[0]);\n  return result;\n}\n\nasync function testUpdateCard(cardId: string) {\n  console.log('✏️ Testing update card...');\n  const result = await mcpRequest('tools/call', {\n    name: 'update_card',\n    arguments: {\n      id: cardId,\n      title: 'Updated Test Card',\n      description: 'This card has been updated via MCP'\n    }\n  });\n  console.log('✅ Update card result:', result.content[0].text.split('\\n')[0]);\n  return result;\n}\n\nasync function testGetCard(cardId: string) {\n  console.log('🔍 Testing get specific card...');\n  const result = await mcpRequest('tools/call', {\n    name: 'get_card',\n    arguments: {\n      id: cardId\n    }\n  });\n  const card = JSON.parse(result.content[0].text);\n  console.log(`✅ Retrieved card: \"${card.title}\" - ${card.status}`);\n  return card;\n}\n\nasync function testBatchMove(cardId: string) {\n  console.log('📦 Testing batch move cards...');\n  const result = await mcpRequest('tools/call', {\n    name: 'batch_move_cards',\n    arguments: {\n      operations: [\n        { cardId, status: 'complete', position: 0 }\n      ]\n    }\n  });\n  console.log('✅ Batch move result:', result.content[0].text.split('\\n')[0]);\n  return result;\n}\n\nasync function testDeleteCard(cardId: string) {\n  console.log('🗑️ Testing delete card...');\n  const result = await mcpRequest('tools/call', {\n    name: 'delete_card',\n    arguments: {\n      id: cardId\n    }\n  });\n  console.log('✅ Delete card result:', result.content[0].text);\n  return result;\n}\n\n// Main test runner\nasync function runTests() {\n  console.log(`🚀 Testing Kanban MCP Integrated Server at ${SERVER_URL}\\n`);\n  \n  try {\n    // Test health and info endpoints\n    await testHealthCheck();\n    await testServerInfo();\n    console.log();\n\n    // Test MCP protocol endpoints\n    await testListTools();\n    console.log();\n\n    // Test card operations\n    await testGetCards();\n    await testGetCardsByStatus();\n    console.log();\n\n    // Test card lifecycle\n    const cardId = await testCreateCard();\n    await testMoveCard(cardId);\n    await testUpdateCard(cardId);\n    await testGetCard(cardId);\n    await testBatchMove(cardId);\n    await testDeleteCard(cardId);\n    console.log();\n\n    console.log('🎉 All tests passed! Integrated MCP Server is working correctly.');\n    console.log('🔗 The MCP server is fully integrated with the Kanban application.');\n    console.log('📡 AI agents can now access the MCP endpoints at the same URL as the web app.');\n\n  } catch (error) {\n    console.error('❌ Test failed:', error);\n    process.exit(1);\n  }\n}\n\n// Run tests if this script is executed directly\nif (import.meta.url === `file://${process.argv[1]}`) {\n  runTests();\n}", "size_bytes": 6355}, "test-mcp-projects.ts": {"content": "#!/usr/bin/env npx tsx\n\n/**\n * Test MCP integration with project support\n */\n\nconst SERVER_URL = 'http://localhost:5000';\n\nasync function mcpRequest(method: string, params: any = {}) {\n  const response = await fetch(`${SERVER_URL}/mcp`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify({\n      jsonrpc: '2.0',\n      id: 1,\n      method,\n      params\n    })\n  });\n\n  if (!response.ok) {\n    throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n  }\n\n  const data = await response.json();\n  if (data.error) {\n    throw new Error(`MCP Error: ${data.error.message}`);\n  }\n\n  return data.result;\n}\n\nasync function testGetProjects() {\n  console.log('📂 Testing get projects...');\n  const result = await mcpRequest('tools/call', {\n    name: 'get_projects',\n    arguments: {}\n  });\n  const projects = JSON.parse(result.content[0].text);\n  console.log(`✅ Found ${projects.length} projects:`, projects.join(', '));\n  return projects;\n}\n\nasync function testGetCardsByProject(project: string) {\n  console.log(`📋 Testing get cards for project: ${project}`);\n  const result = await mcpRequest('tools/call', {\n    name: 'get_cards',\n    arguments: { project }\n  });\n  const cards = JSON.parse(result.content[0].text);\n  console.log(`✅ Found ${cards.length} cards in ${project}`);\n  return cards;\n}\n\nasync function testGetCardsByStatusAndProject(project: string) {\n  console.log(`📊 Testing get cards by status for project: ${project}`);\n  const result = await mcpRequest('tools/call', {\n    name: 'get_cards_by_status',\n    arguments: { project }\n  });\n  const grouped = JSON.parse(result.content[0].text);\n  const statusCounts = Object.keys(grouped).map(status => \n    `${status}: ${grouped[status].length}`\n  ).join(', ');\n  console.log(`✅ Cards by status in ${project}: ${statusCounts}`);\n  return grouped;\n}\n\nasync function testCreateCardInProject(project: string) {\n  console.log(`➕ Testing create card in project: ${project}`);\n  const result = await mcpRequest('tools/call', {\n    name: 'create_card',\n    arguments: {\n      title: `Test Card for ${project}`,\n      description: 'This is a test card created via MCP with project support',\n      project: project,\n      status: 'not-started'\n    }\n  });\n  console.log('✅ Create card result:', result.content[0].text.split('\\n')[0]);\n  \n  // Extract card ID from response\n  const cardData = JSON.parse(result.content[0].text.split(':\\n')[1]);\n  return cardData.id;\n}\n\nasync function runProjectTests() {\n  console.log('🚀 Testing Kanban MCP with Project Support\\n');\n  \n  try {\n    // Test getting all projects\n    const projects = await testGetProjects();\n    console.log();\n\n    // Test each project\n    for (const project of projects.slice(0, 2)) { // Test first 2 projects\n      console.log(`\\n--- Testing Project: ${project} ---`);\n      \n      // Get cards for this project\n      await testGetCardsByProject(project);\n      \n      // Get cards by status for this project\n      await testGetCardsByStatusAndProject(project);\n      \n      // Create a test card in this project\n      const cardId = await testCreateCardInProject(project);\n      \n      console.log(`✅ Created test card ${cardId} in ${project}`);\n    }\n\n    console.log('\\n🎉 All project-based MCP tests passed!');\n\n  } catch (error) {\n    console.error('❌ Test failed:', error);\n    process.exit(1);\n  }\n}\n\n// Run the tests\nrunProjectTests();", "size_bytes": 3451}, "test-mcp-remote.ts": {"content": "#!/usr/bin/env tsx\n\n/**\n * Kanban MCP Remote Server Test Client\n * \n * This script tests the remote MCP server by making direct HTTP requests\n * to verify all tools are working correctly.\n */\n\nconst MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://localhost:3001';\n\ninterface McpRequest {\n  jsonrpc: string;\n  id: number;\n  method: string;\n  params?: any;\n}\n\ninterface McpResponse {\n  jsonrpc: string;\n  id: number;\n  result?: any;\n  error?: any;\n}\n\n// Helper function to make MCP requests\nasync function mcpRequest(method: string, params?: any): Promise<any> {\n  const request: McpRequest = {\n    jsonrpc: \"2.0\",\n    id: Date.now(),\n    method,\n    params\n  };\n\n  const response = await fetch(`${MCP_SERVER_URL}/mcp`, {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify(request)\n  });\n\n  if (!response.ok) {\n    throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n  }\n\n  const data: McpResponse = await response.json();\n  \n  if (data.error) {\n    throw new Error(`MCP Error: ${data.error.message}`);\n  }\n\n  return data.result;\n}\n\n// Test functions\nasync function testHealthCheck() {\n  console.log('🏥 Testing health check...');\n  const response = await fetch(`${MCP_SERVER_URL}/health`);\n  const health = await response.json();\n  console.log('✅ Health check:', health);\n  return health;\n}\n\nasync function testListTools() {\n  console.log('🔧 Testing list tools...');\n  const result = await mcpRequest('tools/list');\n  console.log(`✅ Found ${result.tools.length} tools:`);\n  result.tools.forEach((tool: any) => {\n    console.log(`   • ${tool.name}: ${tool.description}`);\n  });\n  return result.tools;\n}\n\nasync function testGetCards() {\n  console.log('📋 Testing get cards...');\n  const result = await mcpRequest('tools/call', {\n    name: 'get_cards',\n    arguments: {}\n  });\n  console.log('✅ Get cards result:', result.content[0].text.substring(0, 100) + '...');\n  return result;\n}\n\nasync function testCreateCard() {\n  console.log('➕ Testing create card...');\n  const result = await mcpRequest('tools/call', {\n    name: 'create_card',\n    arguments: {\n      title: `Test Card ${Date.now()}`,\n      description: 'This is a test card created via MCP',\n      status: 'not-started'\n    }\n  });\n  console.log('✅ Create card result:', result.content[0].text);\n  \n  // Extract card ID from response\n  const cardData = JSON.parse(result.content[0].text.split(':\\n')[1]);\n  return cardData.id;\n}\n\nasync function testMoveCard(cardId: string) {\n  console.log('🔄 Testing move card...');\n  const result = await mcpRequest('tools/call', {\n    name: 'move_card',\n    arguments: {\n      id: cardId,\n      status: 'in-progress',\n      position: 0\n    }\n  });\n  console.log('✅ Move card result:', result.content[0].text);\n  return result;\n}\n\nasync function testDeleteCard(cardId: string) {\n  console.log('🗑️ Testing delete card...');\n  const result = await mcpRequest('tools/call', {\n    name: 'delete_card',\n    arguments: {\n      id: cardId\n    }\n  });\n  console.log('✅ Delete card result:', result.content[0].text);\n  return result;\n}\n\n// Main test runner\nasync function runTests() {\n  console.log(`🚀 Testing Kanban MCP Remote Server at ${MCP_SERVER_URL}\\n`);\n  \n  try {\n    // Test health check\n    await testHealthCheck();\n    console.log();\n\n    // Test list tools\n    await testListTools();\n    console.log();\n\n    // Test get cards\n    await testGetCards();\n    console.log();\n\n    // Test create card\n    const cardId = await testCreateCard();\n    console.log();\n\n    // Test move card\n    await testMoveCard(cardId);\n    console.log();\n\n    // Test delete card\n    await testDeleteCard(cardId);\n    console.log();\n\n    console.log('🎉 All tests passed! MCP Remote Server is working correctly.');\n\n  } catch (error) {\n    console.error('❌ Test failed:', error);\n    process.exit(1);\n  }\n}\n\n// Run tests if this script is executed directly\nif (import.meta.url === `file://${process.argv[1]}`) {\n  runTests();\n}", "size_bytes": 4017}, "vite.config.ts": {"content": "import { defineConfig } from \"vite\";\nimport react from \"@vitejs/plugin-react\";\nimport path from \"path\";\nimport runtimeErrorOverlay from \"@replit/vite-plugin-runtime-error-modal\";\n\nexport default defineConfig({\n  plugins: [\n    react(),\n    runtimeErrorOverlay(),\n    ...(process.env.NODE_ENV !== \"production\" &&\n    process.env.REPL_ID !== undefined\n      ? [\n          await import(\"@replit/vite-plugin-cartographer\").then((m) =>\n            m.cartographer(),\n          ),\n        ]\n      : []),\n  ],\n  resolve: {\n    alias: {\n      \"@\": path.resolve(import.meta.dirname, \"client\", \"src\"),\n      \"@shared\": path.resolve(import.meta.dirname, \"shared\"),\n      \"@assets\": path.resolve(import.meta.dirname, \"attached_assets\"),\n    },\n  },\n  root: path.resolve(import.meta.dirname, \"client\"),\n  build: {\n    outDir: path.resolve(import.meta.dirname, \"dist/public\"),\n    emptyOutDir: true,\n  },\n  server: {\n    fs: {\n      strict: true,\n      deny: [\"**/.*\"],\n    },\n  },\n});\n", "size_bytes": 971}, "server/db.ts": {"content": "import { Pool, neonConfig } from '@neondatabase/serverless';\nimport { drizzle } from 'drizzle-orm/neon-serverless';\nimport ws from \"ws\";\nimport * as schema from \"@shared/schema\";\n\nneonConfig.webSocketConstructor = ws;\n\nif (!process.env.DATABASE_URL) {\n  throw new Error(\n    \"DATABASE_URL must be set. Did you forget to provision a database?\",\n  );\n}\n\nexport const pool = new Pool({ connectionString: process.env.DATABASE_URL });\nexport const db = drizzle({ client: pool, schema });", "size_bytes": 482}, "server/index.ts": {"content": "import express, { type Request, Response, NextFunction } from \"express\";\nimport { registerRoutes } from \"./routes\";\nimport { setupVite, serveStatic, log } from \"./vite\";\n\nconst app = express();\napp.use(express.json());\napp.use(express.urlencoded({ extended: false }));\n\n// CORS support for MCP endpoints (needed for Claude Desktop)\napp.use((req, res, next) => {\n  if (req.path.startsWith('/mcp')) {\n    res.header('Access-Control-Allow-Origin', '*');\n    res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');\n    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');\n    \n    if (req.method === 'OPTIONS') {\n      res.sendStatus(200);\n      return;\n    }\n  }\n  next();\n});\n\napp.use((req, res, next) => {\n  const start = Date.now();\n  const path = req.path;\n  let capturedJsonResponse: Record<string, any> | undefined = undefined;\n\n  const originalResJson = res.json;\n  res.json = function (bodyJson, ...args) {\n    capturedJsonResponse = bodyJson;\n    return originalResJson.apply(res, [bodyJson, ...args]);\n  };\n\n  res.on(\"finish\", () => {\n    const duration = Date.now() - start;\n    if (path.startsWith(\"/api\") || path.startsWith(\"/mcp\")) {\n      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;\n      if (capturedJsonResponse) {\n        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;\n      }\n\n      if (logLine.length > 80) {\n        logLine = logLine.slice(0, 79) + \"…\";\n      }\n\n      log(logLine);\n    }\n  });\n\n  next();\n});\n\n(async () => {\n  const server = await registerRoutes(app);\n\n  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {\n    const status = err.status || err.statusCode || 500;\n    const message = err.message || \"Internal Server Error\";\n\n    res.status(status).json({ message });\n    throw err;\n  });\n\n  // importantly only setup vite in development and after\n  // setting up all the other routes so the catch-all route\n  // doesn't interfere with the other routes\n  if (app.get(\"env\") === \"development\") {\n    await setupVite(app, server);\n  } else {\n    serveStatic(app);\n  }\n\n  // ALWAYS serve the app on the port specified in the environment variable PORT\n  // Other ports are firewalled. Default to 5000 if not specified.\n  // this serves both the API and the client.\n  // It is the only port that is not firewalled.\n  const port = parseInt(process.env.PORT || '5000', 10);\n  server.listen({\n    port,\n    host: \"0.0.0.0\",\n    reusePort: true,\n  }, () => {\n    log(`serving on port ${port}`);\n  });\n})();\n", "size_bytes": 2564}, "server/routes.ts": {"content": "import type { Express } from \"express\";\nimport { createServer, type Server } from \"http\";\nimport { WebSocketServer, WebSocket } from \"ws\";\nimport { storage } from \"./storage\";\nimport { insertCardSchema, updateCardSchema } from \"@shared/schema\";\nimport {\n  Tool,\n  CallToolResult,\n  TextContent,\n} from \"@modelcontextprotocol/sdk/types.js\";\n\n// WebSocket broadcast helper\nlet wss: WebSocketServer;\n\nfunction broadcast(event: { type: string; data: any }) {\n  if (wss) {\n    wss.clients.forEach((client) => {\n      if (client.readyState === WebSocket.OPEN) {\n        client.send(JSON.stringify(event));\n      }\n    });\n  }\n}\n\nexport async function registerRoutes(app: Express): Promise<Server> {\n  // Get all cards\n  app.get(\"/api/cards\", async (req, res) => {\n    try {\n      const { status, project } = req.query;\n      const cards = await storage.getAllCards(project as string);\n      \n      if (status) {\n        const filteredCards = cards\n          .filter(card => card.status === status)\n          .sort((a, b) => parseInt(a.order) - parseInt(b.order));\n        res.json(filteredCards);\n      } else {\n        res.json(cards);\n      }\n    } catch (error) {\n      res.status(500).json({ message: \"Failed to fetch cards\" });\n    }\n  });\n\n  // Get all projects\n  app.get(\"/api/projects\", async (req, res) => {\n    try {\n      const projects = await storage.getProjects();\n      res.json(projects);\n    } catch (error) {\n      res.status(500).json({ message: \"Failed to fetch projects\" });\n    }\n  });\n\n  // Get cards summary (titles and statuses only)\n  app.get(\"/api/cards/summary\", async (req, res) => {\n    try {\n      const { project } = req.query;\n      const cards = await storage.getAllCards(project as string);\n      \n      // Create summary with just title and status\n      const summary = cards.map(card => ({\n        id: card.id,\n        title: card.title,\n        status: card.status,\n        project: card.project,\n        order: card.order\n      }));\n      \n      res.json(summary);\n    } catch (error) {\n      res.status(500).json({ message: \"Failed to get cards summary\" });\n    }\n  });\n\n  // Get cards summary as markdown\n  app.get(\"/api/cards/summary/markdown\", async (req, res) => {\n    try {\n      const { project } = req.query;\n      const cards = await storage.getAllCards(project as string);\n      \n      // Group cards by status\n      const grouped = cards.reduce((acc, card) => {\n        if (!acc[card.status]) {\n          acc[card.status] = [];\n        }\n        acc[card.status].push(card);\n        return acc;\n      }, {} as Record<string, typeof cards>);\n      \n      // Sort cards within each status by order\n      Object.keys(grouped).forEach(status => {\n        grouped[status].sort((a, b) => parseInt(a.order) - parseInt(b.order));\n      });\n      \n      // Generate markdown\n      let markdown = \"# Cards Summary\\n\\n\";\n      \n      if (project) {\n        markdown += `**Project:** ${project}\\n\\n`;\n      }\n      \n      const statusLabels = {\n        'not-started': 'Not Started',\n        'blocked': 'Blocked',\n        'in-progress': 'In Progress',\n        'complete': 'Complete',\n        'verified': 'Verified'\n      };\n      \n      for (const [status, statusCards] of Object.entries(grouped)) {\n        if (statusCards.length > 0) {\n          markdown += `## ${statusLabels[status as keyof typeof statusLabels] || status} (${statusCards.length})\\n\\n`;\n          statusCards.forEach(card => {\n            markdown += `- ${card.title}\\n`;\n          });\n          markdown += '\\n';\n        }\n      }\n      \n      res.setHeader('Content-Type', 'text/markdown');\n      res.send(markdown);\n    } catch (error) {\n      res.status(500).json({ message: \"Failed to generate markdown summary\" });\n    }\n  });\n\n  // Get cards grouped by status (must come before the :id route)\n  app.get(\"/api/cards/by-status\", async (req, res) => {\n    try {\n      const { project } = req.query;\n      const cards = await storage.getAllCards(project as string);\n      const grouped = cards.reduce((acc, card) => {\n        if (!acc[card.status]) {\n          acc[card.status] = [];\n        }\n        acc[card.status].push(card);\n        return acc;\n      }, {} as Record<string, typeof cards>);\n      \n      // Sort cards within each status by order\n      Object.keys(grouped).forEach(status => {\n        grouped[status].sort((a, b) => parseInt(a.order) - parseInt(b.order));\n      });\n      \n      res.json(grouped);\n    } catch (error) {\n      res.status(500).json({ message: \"Failed to fetch cards by status\" });\n    }\n  });\n\n  // Get single card\n  app.get(\"/api/cards/:id\", async (req, res) => {\n    try {\n      const { id } = req.params;\n      const card = await storage.getCard(id);\n      \n      if (!card) {\n        return res.status(404).json({ message: \"Card not found\" });\n      }\n      \n      res.json(card);\n    } catch (error) {\n      res.status(500).json({ message: \"Failed to fetch card\" });\n    }\n  });\n\n  // Create new card\n  app.post(\"/api/cards\", async (req, res) => {\n    try {\n      const validatedData = insertCardSchema.parse(req.body);\n      const card = await storage.createCard(validatedData);\n      \n      // Broadcast card creation\n      broadcast({ type: \"CARD_CREATED\", data: card });\n      \n      res.status(201).json(card);\n    } catch (error) {\n      if (error instanceof Error && error.name === \"ZodError\") {\n        return res.status(400).json({ \n          message: \"Invalid card data\", \n          errors: JSON.parse(error.message) \n        });\n      }\n      if (error instanceof Error && error.message.includes(\"validation\")) {\n        return res.status(400).json({ message: \"Invalid card data\" });\n      }\n      res.status(500).json({ message: \"Failed to create card\" });\n    }\n  });\n\n  // Update card\n  app.patch(\"/api/cards/:id\", async (req, res) => {\n    try {\n      const { id } = req.params;\n      const validatedData = updateCardSchema.parse(req.body);\n      const card = await storage.updateCard(id, validatedData);\n      \n      // Broadcast card update\n      broadcast({ type: \"CARD_UPDATED\", data: card });\n      \n      res.json(card);\n    } catch (error) {\n      if (error instanceof Error && error.name === \"ZodError\") {\n        return res.status(400).json({ \n          message: \"Invalid card data\", \n          errors: JSON.parse(error.message) \n        });\n      }\n      if (error instanceof Error && error.message.includes(\"not found\")) {\n        return res.status(404).json({ message: \"Card not found\" });\n      }\n      if (error instanceof Error && error.message.includes(\"validation\")) {\n        return res.status(400).json({ message: \"Invalid card data\" });\n      }\n      res.status(500).json({ message: \"Failed to update card\" });\n    }\n  });\n\n  // Delete card\n  app.delete(\"/api/cards/:id\", async (req, res) => {\n    try {\n      const { id } = req.params;\n      const deleted = await storage.deleteCard(id);\n      \n      if (!deleted) {\n        return res.status(404).json({ message: \"Card not found\" });\n      }\n      \n      // Broadcast card deletion\n      broadcast({ type: \"CARD_DELETED\", data: { id } });\n      \n      res.status(204).send();\n    } catch (error) {\n      res.status(500).json({ message: \"Failed to delete card\" });\n    }\n  });\n\n  // Bulk delete cards by IDs\n  app.delete(\"/api/cards/bulk\", async (req, res) => {\n    try {\n      const { ids } = req.body;\n      \n      if (!Array.isArray(ids) || ids.length === 0) {\n        return res.status(400).json({ error: 'ids must be a non-empty array' });\n      }\n      \n      // Delete each card\n      const deletedIds = [];\n      for (const id of ids) {\n        try {\n          const deleted = await storage.deleteCard(id);\n          if (deleted) {\n            deletedIds.push(id);\n          }\n        } catch (error) {\n          console.error(`Error deleting card ${id}:`, error);\n          // Continue with other cards even if one fails\n        }\n      }\n      \n      // Broadcast deletions to all WebSocket clients\n      if (deletedIds.length > 0) {\n        broadcast({\n          type: 'CARDS_BULK_DELETED',\n          data: deletedIds\n        });\n      }\n      \n      res.json({ \n        deletedCount: deletedIds.length,\n        deletedIds,\n        requestedCount: ids.length\n      });\n    } catch (error) {\n      console.error('Error bulk deleting cards:', error);\n      res.status(500).json({ error: 'Failed to bulk delete cards' });\n    }\n  });\n\n  // Move card to specific status (simplified endpoint)\n  app.post(\"/api/cards/:id/move\", async (req, res) => {\n    try {\n      const { id } = req.params;\n      const { status, position } = req.body;\n      \n      if (!status) {\n        return res.status(400).json({ message: \"Status is required\" });\n      }\n      \n      // Get all cards in the target status to calculate order\n      const allCards = await storage.getAllCards();\n      const targetStatusCards = allCards\n        .filter(card => card.status === status && card.id !== id)\n        .sort((a, b) => parseInt(a.order) - parseInt(b.order));\n      \n      let newOrder: string;\n      if (position === undefined || position >= targetStatusCards.length) {\n        // Add to end\n        newOrder = (targetStatusCards.length + 1).toString();\n      } else {\n        // Insert at position - update orders of existing cards\n        newOrder = (position + 1).toString();\n        \n        // Update orders of cards that need to shift down\n        for (let i = position; i < targetStatusCards.length; i++) {\n          const cardToUpdate = targetStatusCards[i];\n          await storage.updateCard(cardToUpdate.id, { \n            order: (parseInt(cardToUpdate.order) + 1).toString() \n          });\n        }\n      }\n      \n      const updatedCard = await storage.updateCard(id, { \n        status, \n        order: newOrder \n      });\n      \n      // Broadcast card move\n      broadcast({ type: \"CARD_UPDATED\", data: updatedCard });\n      \n      res.json(updatedCard);\n    } catch (error) {\n      if (error instanceof Error && error.message.includes(\"not found\")) {\n        return res.status(404).json({ message: \"Card not found\" });\n      }\n      res.status(500).json({ message: \"Failed to move card\" });\n    }\n  });\n\n  // Reorder cards within a status (legacy endpoint - kept for compatibility)\n  app.post(\"/api/cards/reorder\", async (req, res) => {\n    try {\n      const { cardId, newStatus, newOrder } = req.body;\n      \n      if (!cardId || !newStatus || newOrder === undefined) {\n        return res.status(400).json({ message: \"Missing required fields\" });\n      }\n      \n      const card = await storage.updateCard(cardId, { \n        status: newStatus, \n        order: newOrder.toString() \n      });\n      \n      // Broadcast card reorder\n      broadcast({ type: \"CARD_UPDATED\", data: card });\n      \n      res.json(card);\n    } catch (error) {\n      if (error instanceof Error && error.message.includes(\"not found\")) {\n        return res.status(404).json({ message: \"Card not found\" });\n      }\n      res.status(500).json({ message: \"Failed to reorder card\" });\n    }\n  });\n\n  // Batch move multiple cards\n  app.post(\"/api/cards/batch-move\", async (req, res) => {\n    try {\n      const { operations } = req.body;\n      \n      if (!Array.isArray(operations)) {\n        return res.status(400).json({ message: \"Operations must be an array\" });\n      }\n      \n      const results = [];\n      \n      for (const op of operations) {\n        const { cardId, status, position } = op;\n        \n        if (!cardId || !status) {\n          continue;\n        }\n        \n        try {\n          const updatedCard = await storage.updateCard(cardId, { \n            status,\n            order: position !== undefined ? position.toString() : undefined\n          });\n          results.push(updatedCard);\n          \n          // Broadcast each card update\n          broadcast({ type: \"CARD_UPDATED\", data: updatedCard });\n        } catch (error) {\n          console.error(`Failed to move card ${cardId}:`, error);\n        }\n      }\n      \n      res.json({ updated: results, count: results.length });\n    } catch (error) {\n      res.status(500).json({ message: \"Failed to batch move cards\" });\n    }\n  });\n\n  // ===== MCP (Model Context Protocol) Endpoints =====\n\n  // MCP Tools definition\n  const mcpTools: Tool[] = [\n    {\n      name: \"get_projects\",\n      description: \"Get all available projects in the Kanban system.\",\n      inputSchema: {\n        type: \"object\",\n        properties: {},\n        additionalProperties: false\n      }\n    },\n    {\n      name: \"get_cards\",\n      description: \"Get all cards or filter by project and/or status. Returns cards sorted by their order within each status.\",\n      inputSchema: {\n        type: \"object\",\n        properties: {\n          project: {\n            type: \"string\",\n            description: \"Optional: filter cards by specific project\"\n          },\n          status: {\n            type: \"string\",\n            enum: [\"not-started\", \"blocked\", \"in-progress\", \"complete\", \"verified\"],\n            description: \"Optional: filter cards by specific status\"\n          }\n        },\n        additionalProperties: false\n      }\n    },\n    {\n      name: \"get_cards_by_status\",\n      description: \"Get all cards grouped by status with proper ordering. Returns an object with status as keys and arrays of cards as values, optionally filtered by project.\",\n      inputSchema: {\n        type: \"object\",\n        properties: {\n          project: {\n            type: \"string\",\n            description: \"Optional: filter cards by specific project\"\n          }\n        },\n        additionalProperties: false\n      }\n    },\n    {\n      name: \"get_card\",\n      description: \"Get details of a specific card by its ID.\",\n      inputSchema: {\n        type: \"object\",\n        properties: {\n          id: {\n            type: \"string\",\n            description: \"The ID of the card to retrieve\"\n          }\n        },\n        required: [\"id\"],\n        additionalProperties: false\n      }\n    },\n    {\n      name: \"create_card\",\n      description: \"Create a new card in the Kanban board for a specific project. The description field supports full Markdown formatting for better readability and structure.\",\n      inputSchema: {\n        type: \"object\",\n        properties: {\n          title: {\n            type: \"string\",\n            description: \"The title of the card - keep concise and descriptive\"\n          },\n          description: {\n            type: \"string\",\n            description: \"Detailed description of the card in Markdown format. Use Markdown syntax for better formatting: **bold**, *italic*, `code`, [links](url), bullet lists (- item), numbered lists (1. item), headers (## Header), blockquotes (> quote), code blocks (```language code```), and task lists (- [ ] unchecked, - [x] checked) for enhanced readability and structure. Task lists will automatically show progress bars on cards.\"\n          },\n          project: {\n            type: \"string\",\n            description: \"The project this card belongs to\"\n          },\n          link: {\n            type: \"string\",\n            description: \"Optional: URL link related to the card\"\n          },\n          status: {\n            type: \"string\",\n            enum: [\"not-started\", \"blocked\", \"in-progress\", \"complete\", \"verified\"],\n            description: \"The initial status of the card\",\n            default: \"not-started\"\n          }\n        },\n        required: [\"title\", \"description\", \"project\"],\n        additionalProperties: false\n      }\n    },\n    {\n      name: \"move_card\",\n      description: \"Move a card to a different status and optionally specify its position within that status.\",\n      inputSchema: {\n        type: \"object\",\n        properties: {\n          id: {\n            type: \"string\",\n            description: \"The ID of the card to move\"\n          },\n          status: {\n            type: \"string\",\n            enum: [\"not-started\", \"blocked\", \"in-progress\", \"complete\", \"verified\"],\n            description: \"The target status to move the card to\"\n          },\n          position: {\n            type: \"number\",\n            description: \"Optional: position within the target status (0 = top, omit to add to end)\"\n          }\n        },\n        required: [\"id\", \"status\"],\n        additionalProperties: false\n      }\n    },\n    {\n      name: \"update_card\",\n      description: \"Update properties of an existing card. Use Markdown formatting in the description for better readability.\",\n      inputSchema: {\n        type: \"object\",\n        properties: {\n          id: {\n            type: \"string\",\n            description: \"The ID of the card to update\"\n          },\n          title: {\n            type: \"string\",\n            description: \"Optional: new title for the card\"\n          },\n          description: {\n            type: \"string\",\n            description: \"Optional: new description for the card in Markdown format. Use **bold**, *italic*, `code`, lists, headers, blockquotes, code blocks, and task lists (- [ ] unchecked, - [x] checked) for better structure and readability. Task lists will show progress bars.\"\n          },\n          link: {\n            type: \"string\",\n            description: \"Optional: new link for the card\"\n          },\n          status: {\n            type: \"string\",\n            enum: [\"not-started\", \"blocked\", \"in-progress\", \"complete\", \"verified\"],\n            description: \"Optional: new status for the card\"\n          }\n        },\n        required: [\"id\"],\n        additionalProperties: false\n      }\n    },\n    {\n      name: \"delete_card\",\n      description: \"Delete a card from the Kanban board.\",\n      inputSchema: {\n        type: \"object\",\n        properties: {\n          id: {\n            type: \"string\",\n            description: \"The ID of the card to delete\"\n          }\n        },\n        required: [\"id\"],\n        additionalProperties: false\n      }\n    },\n    {\n      name: \"bulk_delete_cards\",\n      description: \"Delete multiple cards from the Kanban board by their IDs. This is more efficient than deleting cards one by one.\",\n      inputSchema: {\n        type: \"object\",\n        properties: {\n          ids: {\n            type: \"array\",\n            items: {\n              type: \"string\"\n            },\n            description: \"Array of card IDs to delete\",\n            minItems: 1\n          }\n        },\n        required: [\"ids\"],\n        additionalProperties: false\n      }\n    },\n    {\n      name: \"batch_move_cards\",\n      description: \"Move multiple cards in a single operation for better performance.\",\n      inputSchema: {\n        type: \"object\",\n        properties: {\n          operations: {\n            type: \"array\",\n            items: {\n              type: \"object\",\n              properties: {\n                cardId: {\n                  type: \"string\",\n                  description: \"The ID of the card to move\"\n                },\n                status: {\n                  type: \"string\",\n                  enum: [\"not-started\", \"blocked\", \"in-progress\", \"complete\", \"verified\"],\n                  description: \"The target status\"\n                },\n                position: {\n                  type: \"number\",\n                  description: \"Optional: position within the target status\"\n                }\n              },\n              required: [\"cardId\", \"status\"],\n              additionalProperties: false\n            },\n            description: \"Array of move operations to perform\"\n          }\n        },\n        required: [\"operations\"],\n        additionalProperties: false\n      }\n    }\n  ];\n\n  // MCP Tool execution function\n  async function executeMcpTool(name: string, args: any): Promise<CallToolResult> {\n    try {\n      switch (name) {\n        case \"get_projects\": {\n          const projects = await storage.getProjects();\n          return {\n            content: [\n              {\n                type: \"text\",\n                text: JSON.stringify(projects, null, 2)\n              } as TextContent\n            ]\n          };\n        }\n\n        case \"get_cards\": {\n          const { status, project } = args as { status?: string; project?: string };\n          const cards = await storage.getAllCards(project);\n          \n          if (status) {\n            const filteredCards = cards\n              .filter(card => card.status === status)\n              .sort((a, b) => parseInt(a.order) - parseInt(b.order));\n            return {\n              content: [\n                {\n                  type: \"text\",\n                  text: JSON.stringify(filteredCards, null, 2)\n                } as TextContent\n              ]\n            };\n          } else {\n            return {\n              content: [\n                {\n                  type: \"text\",\n                  text: JSON.stringify(cards, null, 2)\n                } as TextContent\n              ]\n            };\n          }\n        }\n        \n        case \"get_cards_by_status\": {\n          const { project } = args as { project?: string };\n          const cards = await storage.getAllCards(project);\n          const grouped = cards.reduce((acc, card) => {\n            if (!acc[card.status]) {\n              acc[card.status] = [];\n            }\n            acc[card.status].push(card);\n            return acc;\n          }, {} as Record<string, typeof cards>);\n          \n          // Sort cards within each status by order\n          Object.keys(grouped).forEach(status => {\n            grouped[status].sort((a, b) => parseInt(a.order) - parseInt(b.order));\n          });\n          \n          return {\n            content: [\n              {\n                type: \"text\",\n                text: JSON.stringify(grouped, null, 2)\n              } as TextContent\n            ]\n          };\n        }\n        \n        case \"get_card\": {\n          const { id } = args as { id: string };\n          const card = await storage.getCard(id);\n          \n          return {\n            content: [\n              {\n                type: \"text\",\n                text: JSON.stringify(card, null, 2)\n              } as TextContent\n            ]\n          };\n        }\n        \n        case \"create_card\": {\n          const { title, description, project, link, status = \"not-started\" } = args as {\n            title: string;\n            description: string;\n            project: string;\n            link?: string;\n            status?: string;\n          };\n          \n          try {\n            // Validate the card data using the schema\n            const validatedData = insertCardSchema.parse({\n              title,\n              description,\n              project,\n              link: link || undefined,\n              status\n            });\n            \n            const card = await storage.createCard(validatedData);\n            \n            // Broadcast card creation\n            broadcast({ type: \"CARD_CREATED\", data: card });\n            \n            return {\n              content: [\n                {\n                  type: \"text\",\n                  text: `Card created successfully:\\n${JSON.stringify(card, null, 2)}`\n                } as TextContent\n              ]\n            };\n          } catch (error) {\n            if (error instanceof Error && error.name === \"ZodError\") {\n              return {\n                content: [\n                  {\n                    type: \"text\",\n                    text: `Invalid card data. Status must be one of: not-started, blocked, in-progress, complete, verified. Received: ${status}`\n                  } as TextContent\n                ]\n              };\n            }\n            throw error;\n          }\n        }\n        \n        case \"move_card\": {\n          const { id, status, position } = args as {\n            id: string;\n            status: string;\n            position?: number;\n          };\n          \n          try {\n            // Validate the update data using the schema\n            const updateData: any = { status };\n            if (position !== undefined) {\n              updateData.order = position.toString();\n            }\n            \n            const validatedData = updateCardSchema.parse(updateData);\n            const card = await storage.updateCard(id, validatedData);\n            \n            // Broadcast card update\n            broadcast({ type: \"CARD_UPDATED\", data: card });\n            \n            return {\n              content: [\n                {\n                  type: \"text\",\n                  text: `Card moved successfully:\\n${JSON.stringify(card, null, 2)}`\n                } as TextContent\n              ]\n            };\n          } catch (error) {\n            if (error instanceof Error && error.name === \"ZodError\") {\n              return {\n                content: [\n                  {\n                    type: \"text\",\n                    text: `Invalid status. Status must be one of: not-started, blocked, in-progress, complete, verified. Received: ${status}`\n                  } as TextContent\n                ]\n              };\n            }\n            throw error;\n          }\n        }\n        \n        case \"update_card\": {\n          const { id, ...updates } = args as {\n            id: string;\n            title?: string;\n            description?: string;\n            link?: string;\n            status?: string;\n          };\n          \n          try {\n            // Validate the update data using the schema\n            const validatedData = updateCardSchema.parse(updates);\n            const card = await storage.updateCard(id, validatedData);\n            \n            // Broadcast card update\n            broadcast({ type: \"CARD_UPDATED\", data: card });\n            \n            return {\n              content: [\n                {\n                  type: \"text\",\n                  text: `Card updated successfully:\\n${JSON.stringify(card, null, 2)}`\n                } as TextContent\n              ]\n            };\n          } catch (error) {\n            if (error instanceof Error && error.name === \"ZodError\") {\n              const statusError = updates.status ? ` Status must be one of: not-started, blocked, in-progress, complete, verified. Received: ${updates.status}` : '';\n              return {\n                content: [\n                  {\n                    type: \"text\",\n                    text: `Invalid card data.${statusError}`\n                  } as TextContent\n                ]\n              };\n            }\n            throw error;\n          }\n        }\n        \n        case \"delete_card\": {\n          const { id } = args as { id: string };\n          await storage.deleteCard(id);\n          \n          // Broadcast card deletion\n          broadcast({ type: \"CARD_DELETED\", data: { id } });\n          \n          return {\n            content: [\n              {\n                type: \"text\",\n                text: `Card ${id} deleted successfully`\n              } as TextContent\n            ]\n          };\n        }\n        \n        case \"bulk_delete_cards\": {\n          const { ids } = args as { ids: string[] };\n          \n          if (!Array.isArray(ids) || ids.length === 0) {\n            return {\n              content: [\n                {\n                  type: \"text\",\n                  text: \"Error: ids must be a non-empty array\"\n                } as TextContent\n              ],\n              isError: true\n            };\n          }\n          \n          const deletedIds = [];\n          const failedIds = [];\n          \n          for (const id of ids) {\n            try {\n              const deleted = await storage.deleteCard(id);\n              if (deleted) {\n                deletedIds.push(id);\n              } else {\n                failedIds.push(id);\n              }\n            } catch (error) {\n              console.error(`Error deleting card ${id}:`, error);\n              failedIds.push(id);\n            }\n          }\n          \n          // Broadcast bulk deletion\n          if (deletedIds.length > 0) {\n            broadcast({\n              type: 'CARDS_BULK_DELETED',\n              data: deletedIds\n            });\n          }\n          \n          const result = {\n            deletedCount: deletedIds.length,\n            deletedIds,\n            failedCount: failedIds.length,\n            failedIds,\n            requestedCount: ids.length\n          };\n          \n          return {\n            content: [\n              {\n                type: \"text\",\n                text: `Bulk delete completed:\\n${JSON.stringify(result, null, 2)}`\n              } as TextContent\n            ]\n          };\n        }\n        \n        case \"batch_move_cards\": {\n          const { operations } = args as { operations: Array<{ cardId: string; status: string; position?: number }> };\n          const results = [];\n          \n          for (const op of operations) {\n            const { cardId, status, position } = op;\n            \n            if (!cardId || !status) {\n              continue;\n            }\n            \n            try {\n              const updateData: any = { status };\n              if (position !== undefined) {\n                updateData.order = position.toString();\n              }\n              \n              const updatedCard = await storage.updateCard(cardId, updateData);\n              results.push(updatedCard);\n              \n              // Broadcast each card update\n              broadcast({ type: \"CARD_UPDATED\", data: updatedCard });\n            } catch (error) {\n              console.error(`Failed to move card ${cardId}:`, error);\n            }\n          }\n          \n          return {\n            content: [\n              {\n                type: \"text\",\n                text: `Batch move completed:\\n${JSON.stringify({ updated: results, count: results.length }, null, 2)}`\n              } as TextContent\n            ]\n          };\n        }\n        \n        default:\n          throw new Error(`Tool ${name} not found`);\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      return {\n        content: [\n          {\n            type: \"text\",\n            text: `Error: ${errorMessage}`\n          } as TextContent\n        ],\n        isError: true\n      };\n    }\n  }\n\n  // MCP Health check endpoint\n  app.get('/mcp/health', (req, res) => {\n    res.json({ \n      status: 'healthy', \n      mcpServer: 'kanban-integrated-server',\n      version: '1.0.0',\n      timestamp: new Date().toISOString()\n    });\n  });\n\n  // MCP Info endpoint\n  app.get('/mcp/info', (req, res) => {\n    res.json({\n      name: 'Kanban MCP Integrated Server',\n      version: '1.0.0',\n      description: 'Model Context Protocol server integrated with Kanban board application',\n      tools: mcpTools.map(tool => ({\n        name: tool.name,\n        description: tool.description\n      })),\n      endpoints: {\n        health: '/mcp/health',\n        info: '/mcp/info',\n        mcp: '/mcp'\n      }\n    });\n  });\n\n  // MCP Protocol endpoint (GET handler for info)\n  app.get('/mcp', (req, res) => {\n    res.json({\n      error: \"MCP endpoint requires POST requests with JSON-RPC 2.0 format\",\n      usage: \"This is a Model Context Protocol (MCP) server endpoint\",\n      examples: {\n        listTools: {\n          method: \"POST\",\n          url: \"/mcp\",\n          body: {\n            jsonrpc: \"2.0\",\n            id: 1,\n            method: \"tools/list\",\n            params: {}\n          }\n        },\n        callTool: {\n          method: \"POST\", \n          url: \"/mcp\",\n          body: {\n            jsonrpc: \"2.0\",\n            id: 2,\n            method: \"tools/call\",\n            params: {\n              name: \"get_cards\",\n              arguments: {}\n            }\n          }\n        }\n      },\n      endpoints: {\n        health: \"/mcp/health\",\n        info: \"/mcp/info\",\n        mcp: \"/mcp (POST only)\"\n      }\n    });\n  });\n\n  // MCP Protocol endpoint (JSON-RPC 2.0)\n  app.post('/mcp', async (req, res) => {\n    try {\n      const { jsonrpc, id, method, params } = req.body;\n      \n      // Debug logging\n      console.log(`[MCP] Request: ${method}`, params ? JSON.stringify(params) : 'no params');\n\n      if (jsonrpc !== \"2.0\") {\n        return res.status(400).json({\n          jsonrpc: \"2.0\",\n          id,\n          error: { code: -32600, message: \"Invalid Request\" }\n        });\n      }\n\n      switch (method) {\n        case \"initialize\":\n          // MCP initialization handshake - match client protocol version\n          const clientProtocolVersion = params?.protocolVersion || \"2024-11-05\";\n          res.json({\n            jsonrpc: \"2.0\",\n            id,\n            result: {\n              protocolVersion: clientProtocolVersion,\n              capabilities: {\n                tools: {\n                  listChanged: true\n                },\n                logging: {}\n              },\n              serverInfo: {\n                name: \"kanban-integrated-server\",\n                version: \"1.0.0\"\n              }\n            }\n          });\n          break;\n\n        case \"tools/list\":\n          res.json({\n            jsonrpc: \"2.0\",\n            id,\n            result: { tools: mcpTools }\n          });\n          break;\n\n        case \"tools/call\":\n          const { name, arguments: args } = params;\n          const result = await executeMcpTool(name, args || {});\n          res.json({\n            jsonrpc: \"2.0\",\n            id,\n            result\n          });\n          break;\n\n        case \"ping\":\n          // Handle ping requests\n          res.json({\n            jsonrpc: \"2.0\",\n            id,\n            result: {}\n          });\n          break;\n\n        case \"notifications/initialized\":\n          // Handle initialization notification (no response needed for notifications)\n          res.status(204).send();\n          return;\n\n        default:\n          console.log(`[MCP] Unknown method: ${method}`);\n          res.status(404).json({\n            jsonrpc: \"2.0\",\n            id,\n            error: { code: -32601, message: `Method not found: ${method}` }\n          });\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[MCP] Error:`, errorMessage);\n      res.status(500).json({\n        jsonrpc: \"2.0\",\n        id: req.body?.id || null,\n        error: { code: -32603, message: `Internal error: ${errorMessage}` }\n      });\n    }\n  });\n\n  const httpServer = createServer(app);\n  \n  // Setup WebSocket server\n  wss = new WebSocketServer({ server: httpServer, path: '/ws' });\n  \n  wss.on('connection', (ws) => {\n    console.log('New WebSocket connection established');\n    \n    // Send initial data to new client\n    storage.getAllCards().then(cards => {\n      ws.send(JSON.stringify({ type: \"INITIAL_DATA\", data: cards }));\n    });\n    \n    ws.on('close', () => {\n      console.log('WebSocket connection closed');\n    });\n    \n    ws.on('error', (error) => {\n      console.error('WebSocket error:', error);\n    });\n  });\n  \n  return httpServer;\n}\n", "size_bytes": 34792}, "server/storage.ts": {"content": "import { Card, InsertCard, UpdateCard, User, InsertUser, cards, users } from \"@shared/schema\";\nimport { db } from \"./db\";\nimport { eq, desc } from \"drizzle-orm\";\n\nexport interface IStorage {\n  // Card operations\n  getAllCards(project?: string): Promise<Card[]>;\n  getCard(id: string): Promise<Card | undefined>;\n  createCard(card: InsertCard): Promise<Card>;\n  updateCard(id: string, updates: UpdateCard): Promise<Card>;\n  deleteCard(id: string): Promise<boolean>;\n  \n  // Project operations\n  getProjects(): Promise<string[]>;\n  \n  // User operations (for future authentication)\n  getUser(id: string): Promise<User | undefined>;\n  getUserByUsername(username: string): Promise<User | undefined>;\n  createUser(user: InsertUser): Promise<User>;\n}\n\nexport class DatabaseStorage implements IStorage {\n  async getUser(id: string): Promise<User | undefined> {\n    const [user] = await db.select().from(users).where(eq(users.id, id));\n    return user || undefined;\n  }\n\n  async getUserByUsername(username: string): Promise<User | undefined> {\n    const [user] = await db.select().from(users).where(eq(users.username, username));\n    return user || undefined;\n  }\n\n  async createUser(insertUser: InsertUser): Promise<User> {\n    const [user] = await db\n      .insert(users)\n      .values(insertUser)\n      .returning();\n    return user;\n  }\n\n  async getAllCards(project?: string): Promise<Card[]> {\n    let allCards: Card[];\n    \n    if (project) {\n      allCards = await db.select().from(cards).where(eq(cards.project, project));\n    } else {\n      allCards = await db.select().from(cards);\n    }\n    \n    return allCards.sort((a, b) => parseInt(a.order) - parseInt(b.order));\n  }\n\n  async getProjects(): Promise<string[]> {\n    const allCards = await db.select({ project: cards.project }).from(cards);\n    const projects = new Set(allCards.map(card => card.project));\n    return Array.from(projects).sort();\n  }\n\n  async getCard(id: string): Promise<Card | undefined> {\n    const [card] = await db.select().from(cards).where(eq(cards.id, id));\n    return card || undefined;\n  }\n\n  async createCard(insertCard: InsertCard): Promise<Card> {\n    const [card] = await db\n      .insert(cards)\n      .values({\n        ...insertCard,\n        project: insertCard.project || \"default\"\n      })\n      .returning();\n    return card;\n  }\n\n  async updateCard(id: string, updates: UpdateCard): Promise<Card> {\n    const [updatedCard] = await db\n      .update(cards)\n      .set(updates)\n      .where(eq(cards.id, id))\n      .returning();\n    \n    if (!updatedCard) {\n      throw new Error(`Card with id ${id} not found`);\n    }\n    \n    return updatedCard;\n  }\n\n  async deleteCard(id: string): Promise<boolean> {\n    const result = await db\n      .delete(cards)\n      .where(eq(cards.id, id))\n      .returning({ id: cards.id });\n    \n    return result.length > 0;\n  }\n}\n\nexport const storage = new DatabaseStorage();", "size_bytes": 2895}, "server/vite.ts": {"content": "import express, { type Express } from \"express\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { createServer as createViteServer, createLogger } from \"vite\";\nimport { type Server } from \"http\";\nimport viteConfig from \"../vite.config\";\nimport { nanoid } from \"nanoid\";\n\nconst viteLogger = createLogger();\n\nexport function log(message: string, source = \"express\") {\n  const formattedTime = new Date().toLocaleTimeString(\"en-US\", {\n    hour: \"numeric\",\n    minute: \"2-digit\",\n    second: \"2-digit\",\n    hour12: true,\n  });\n\n  console.log(`${formattedTime} [${source}] ${message}`);\n}\n\nexport async function setupVite(app: Express, server: Server) {\n  const serverOptions = {\n    middlewareMode: true,\n    hmr: { server },\n    allowedHosts: true as const,\n  };\n\n  const vite = await createViteServer({\n    ...viteConfig,\n    configFile: false,\n    customLogger: {\n      ...viteLogger,\n      error: (msg, options) => {\n        viteLogger.error(msg, options);\n        process.exit(1);\n      },\n    },\n    server: serverOptions,\n    appType: \"custom\",\n  });\n\n  app.use(vite.middlewares);\n  app.use(\"*\", async (req, res, next) => {\n    const url = req.originalUrl;\n\n    try {\n      const clientTemplate = path.resolve(\n        import.meta.dirname,\n        \"..\",\n        \"client\",\n        \"index.html\",\n      );\n\n      // always reload the index.html file from disk incase it changes\n      let template = await fs.promises.readFile(clientTemplate, \"utf-8\");\n      template = template.replace(\n        `src=\"/src/main.tsx\"`,\n        `src=\"/src/main.tsx?v=${nanoid()}\"`,\n      );\n      const page = await vite.transformIndexHtml(url, template);\n      res.status(200).set({ \"Content-Type\": \"text/html\" }).end(page);\n    } catch (e) {\n      vite.ssrFixStacktrace(e as Error);\n      next(e);\n    }\n  });\n}\n\nexport function serveStatic(app: Express) {\n  const distPath = path.resolve(import.meta.dirname, \"public\");\n\n  if (!fs.existsSync(distPath)) {\n    throw new Error(\n      `Could not find the build directory: ${distPath}, make sure to build the client first`,\n    );\n  }\n\n  app.use(express.static(distPath));\n\n  // fall through to index.html if the file doesn't exist\n  app.use(\"*\", (_req, res) => {\n    res.sendFile(path.resolve(distPath, \"index.html\"));\n  });\n}\n", "size_bytes": 2263}, "shared/schema.ts": {"content": "import { sql } from \"drizzle-orm\";\nimport { pgTable, text, varchar } from \"drizzle-orm/pg-core\";\nimport { createInsertSchema } from \"drizzle-zod\";\nimport { z } from \"zod\";\n\nexport const KANBAN_STATUSES = [\n  \"not-started\",\n  \"blocked\", \n  \"in-progress\",\n  \"complete\",\n  \"verified\"\n] as const;\n\nexport type KanbanStatus = typeof KANBAN_STATUSES[number];\n\nexport const cards = pgTable(\"cards\", {\n  id: varchar(\"id\").primaryKey().default(sql`gen_random_uuid()`),\n  title: text(\"title\").notNull(),\n  description: text(\"description\").notNull(),\n  link: text(\"link\"),\n  status: text(\"status\").notNull().default(\"not-started\"),\n  order: text(\"order\").notNull().default(\"0\"),\n  project: text(\"project\").notNull().default(\"default\"),\n  taskList: text(\"task_list\"), // JSON array of task items with completion status\n});\n\nexport const insertCardSchema = createInsertSchema(cards).omit({\n  id: true,\n}).extend({\n  status: z.enum(KANBAN_STATUSES).default(\"not-started\"),\n});\n\nexport const updateCardSchema = createInsertSchema(cards).omit({\n  id: true,\n}).extend({\n  status: z.enum(KANBAN_STATUSES).optional(),\n}).partial();\n\nexport type InsertCard = z.infer<typeof insertCardSchema>;\nexport type UpdateCard = z.infer<typeof updateCardSchema>;\nexport type Card = typeof cards.$inferSelect;\n\nexport const users = pgTable(\"users\", {\n  id: varchar(\"id\").primaryKey().default(sql`gen_random_uuid()`),\n  username: text(\"username\").notNull().unique(),\n  password: text(\"password\").notNull(),\n});\n\nexport const insertUserSchema = createInsertSchema(users).pick({\n  username: true,\n  password: true,\n});\n\nexport type InsertUser = z.infer<typeof insertUserSchema>;\nexport type User = typeof users.$inferSelect;\n", "size_bytes": 1690}, "client/src/App.tsx": {"content": "import { Switch, Route } from \"wouter\";\nimport { queryClient } from \"./lib/queryClient\";\nimport { QueryClientProvider } from \"@tanstack/react-query\";\nimport { Toaster } from \"@/components/ui/toaster\";\nimport { TooltipProvider } from \"@/components/ui/tooltip\";\nimport { ThemeProvider } from \"@/components/theme-provider\";\nimport NotFound from \"@/pages/not-found\";\nimport Kanban from \"@/pages/kanban\";\n\nfunction Router() {\n  return (\n    <Switch>\n      <Route path=\"/\" component={Kanban} />\n      <Route path=\"/project/:project\" component={Kanban} />\n      <Route component={NotFound} />\n    </Switch>\n  );\n}\n\nfunction App() {\n  return (\n    <ThemeProvider defaultTheme=\"light\">\n      <QueryClientProvider client={queryClient}>\n        <TooltipProvider>\n          <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 transition-colors duration-300\">\n            <Toaster />\n            <Router />\n          </div>\n        </TooltipProvider>\n      </QueryClientProvider>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n", "size_bytes": 1101}, "client/src/index.css": {"content": "@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n:root {\n  /* Modern vibrant background gradient */\n  --background: linear-gradient(135deg, hsl(240, 100%, 99%) 0%, hsl(210, 100%, 98%) 100%);\n  --background-solid: hsl(240, 100%, 99%);\n  --foreground: hsl(220, 15%, 8%);\n  --muted: hsl(220, 13%, 91%);\n  --muted-foreground: hsl(220, 9%, 46%);\n  --popover: hsl(0, 0%, 100%);\n  --popover-foreground: hsl(220, 15%, 8%);\n  --card: hsl(0, 0%, 100%);\n  --card-foreground: hsl(220, 15%, 8%);\n  --border: hsl(220, 13%, 91%);\n  --input: hsl(220, 13%, 91%);\n  --primary: linear-gradient(135deg, hsl(221, 83%, 53%) 0%, hsl(230, 89%, 65%) 100%);\n  --primary-solid: hsl(221, 83%, 53%);\n  --primary-foreground: hsl(210, 40%, 98%);\n  --secondary: hsl(220, 14%, 96%);\n  --secondary-foreground: hsl(220, 9%, 9%);\n  --accent: hsl(220, 14%, 96%);\n  --accent-foreground: hsl(220, 9%, 9%);\n  --destructive: linear-gradient(135deg, hsl(0, 84%, 60%) 0%, hsl(0, 70%, 50%) 100%);\n  --destructive-solid: hsl(0, 84%, 60%);\n  --destructive-foreground: hsl(210, 40%, 98%);\n  --ring: hsl(221, 83%, 53%);\n  --radius: 0.75rem;\n  \n  /* Vibrant Kanban status colors with gradients */\n  --status-not-started: linear-gradient(135deg, hsl(220, 14%, 70%) 0%, hsl(215, 16%, 65%) 100%);\n  --status-not-started-solid: hsl(220, 14%, 70%);\n  --status-blocked: linear-gradient(135deg, hsl(0, 84%, 60%) 0%, hsl(10, 90%, 58%) 100%);\n  --status-blocked-solid: hsl(0, 84%, 60%);\n  --status-in-progress: linear-gradient(135deg, hsl(217, 91%, 60%) 0%, hsl(210, 100%, 56%) 100%);\n  --status-in-progress-solid: hsl(217, 91%, 60%);\n  --status-complete: linear-gradient(135deg, hsl(142, 76%, 36%) 0%, hsl(158, 64%, 52%) 100%);\n  --status-complete-solid: hsl(142, 76%, 36%);\n  --status-verified: linear-gradient(135deg, hsl(262, 83%, 58%) 0%, hsl(280, 100%, 70%) 100%);\n  --status-verified-solid: hsl(262, 83%, 58%);\n}\n\n.dark {\n  /* Dark mode background gradient */\n  --background: linear-gradient(135deg, hsl(240, 10%, 3.9%) 0%, hsl(235, 15%, 5%) 100%);\n  --background-solid: hsl(240, 10%, 3.9%);\n  --foreground: hsl(0, 0%, 98%);\n  --muted: hsl(240, 3.7%, 15.9%);\n  --muted-foreground: hsl(240, 5%, 64.9%);\n  --popover: hsl(240, 10%, 8%);\n  --popover-foreground: hsl(0, 0%, 98%);\n  --card: hsl(240, 10%, 8%);\n  --card-foreground: hsl(0, 0%, 98%);\n  --border: hsl(240, 3.7%, 15.9%);\n  --input: hsl(240, 3.7%, 15.9%);\n  --primary: linear-gradient(135deg, hsl(210, 90%, 50%) 0%, hsl(220, 89%, 60%) 100%);\n  --primary-solid: hsl(210, 90%, 50%);\n  --primary-foreground: hsl(211, 100%, 99%);\n  --secondary: hsl(240, 3.7%, 15.9%);\n  --secondary-foreground: hsl(0, 0%, 98%);\n  --accent: hsl(240, 3.7%, 15.9%);\n  --accent-foreground: hsl(0, 0%, 98%);\n  --destructive: linear-gradient(135deg, hsl(0, 62.8%, 50%) 0%, hsl(10, 70%, 45%) 100%);\n  --destructive-solid: hsl(0, 62.8%, 50%);\n  --destructive-foreground: hsl(0, 0%, 98%);\n  --ring: hsl(240, 4.9%, 83.9%);\n  --radius: 0.75rem;\n  \n  /* Dark mode Kanban status colors */\n  --status-not-started: linear-gradient(135deg, hsl(220, 14%, 40%) 0%, hsl(215, 16%, 35%) 100%);\n  --status-not-started-solid: hsl(220, 14%, 40%);\n  --status-blocked: linear-gradient(135deg, hsl(0, 70%, 50%) 0%, hsl(10, 80%, 45%) 100%);\n  --status-blocked-solid: hsl(0, 70%, 50%);\n  --status-in-progress: linear-gradient(135deg, hsl(217, 80%, 55%) 0%, hsl(210, 90%, 50%) 100%);\n  --status-in-progress-solid: hsl(217, 80%, 55%);\n  --status-complete: linear-gradient(135deg, hsl(142, 70%, 45%) 0%, hsl(158, 60%, 50%) 100%);\n  --status-complete-solid: hsl(142, 70%, 45%);\n  --status-verified: linear-gradient(135deg, hsl(262, 80%, 65%) 0%, hsl(280, 90%, 70%) 100%);\n  --status-verified-solid: hsl(262, 80%, 65%);\n}\n\n@layer base {\n  * {\n    @apply border-border;\n  }\n\n  body {\n    @apply font-sans antialiased text-foreground;\n    font-family: 'Inter', system-ui, -apple-system, sans-serif;\n    background: var(--background);\n    min-height: 100vh;\n  }\n}\n\n@layer utilities {\n  .line-clamp-2 {\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n  }\n  \n  .line-clamp-3 {\n    display: -webkit-box;\n    -webkit-line-clamp: 3;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n  }\n}\n\n/* Enhanced Kanban specific styles */\n.kanban-column {\n  @apply transition-all duration-300 ease-in-out;\n  background: linear-gradient(145deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.95) 100%);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255,255,255,0.2);\n  box-shadow: 0 8px 32px rgba(0,0,0,0.1);\n}\n\n.task-card {\n  @apply transition-all duration-300 ease-in-out;\n  transform-origin: center;\n  background: linear-gradient(145deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.98) 100%);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255,255,255,0.3);\n  box-shadow: 0 4px 20px rgba(0,0,0,0.08), 0 1px 3px rgba(0,0,0,0.1);\n}\n\n.task-card:hover {\n  transform: translateY(-4px) scale(1.02);\n  box-shadow: 0 12px 40px rgba(0,0,0,0.15), 0 4px 12px rgba(0,0,0,0.1);\n  border: 1px solid rgba(99, 102, 241, 0.2);\n}\n\n/* Drag and drop animations */\n.sortable-ghost {\n  @apply opacity-30;\n}\n\n.sortable-chosen {\n  @apply scale-105 rotate-1;\n}\n\n/* Smooth card transitions */\n.task-card {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n/* Prevent layout shift during drag */\n[data-dnd-kit-dropped] {\n  animation: card-drop 0.3s ease-out;\n}\n\n@keyframes card-drop {\n  0% {\n    transform: scale(1.05) rotate(2deg);\n    opacity: 0.9;\n  }\n  100% {\n    transform: scale(1) rotate(0deg);\n    opacity: 1;\n  }\n}\n\n/* Markdown prose styles for cards */\n.task-card .prose {\n  @apply text-gray-600;\n}\n\n.task-card .prose p:last-child {\n  @apply mb-0;\n}\n\n.task-card .prose pre {\n  @apply bg-gray-50 text-xs p-2 rounded border overflow-x-auto;\n}\n\n.task-card .prose code {\n  @apply bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-xs font-mono;\n}\n\n.task-card .prose blockquote {\n  @apply border-l-2 border-blue-200 pl-3 italic text-gray-700;\n}\n\n.task-card .prose ul, .task-card .prose ol {\n  @apply text-xs;\n}\n\n.task-card .prose h1, .task-card .prose h2, .task-card .prose h3 {\n  @apply text-gray-800;\n}\n\n.task-card .prose a {\n  @apply text-blue-600 hover:text-blue-800 no-underline hover:underline;\n}\n", "size_bytes": 6216}, "client/src/main.tsx": {"content": "import { createRoot } from \"react-dom/client\";\nimport App from \"./App\";\nimport \"./index.css\";\n\ncreateRoot(document.getElementById(\"root\")!).render(<App />);\n", "size_bytes": 157}, "client/src/components/add-card-dialog.tsx": {"content": "import { useState } from \"react\";\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { insertCardSchema, InsertCard } from \"@shared/schema\";\nimport { apiRequest } from \"@/lib/queryClient\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport {\n  Dialog,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Button } from \"@/components/ui/button\";\nimport { KANBAN_STATUSES } from \"@shared/schema\";\n\ninterface AddCardDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  project: string;\n}\n\nexport function AddCardDialog({ open, onOpenChange, project }: AddCardDialogProps) {\n  const { toast } = useToast();\n  const queryClient = useQueryClient();\n\n  const form = useForm<InsertCard>({\n    resolver: zodResolver(insertCardSchema),\n    defaultValues: {\n      title: \"\",\n      description: \"\",\n      link: \"\",\n      status: \"not-started\",\n      project,\n    },\n  });\n\n  const createCardMutation = useMutation({\n    mutationFn: async (cardData: InsertCard) => {\n      const response = await apiRequest(\"POST\", \"/api/cards\", cardData);\n      return response.json();\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: [\"/api/cards\", project] });\n      toast({\n        title: \"Card created\",\n        description: \"New card has been added to the board.\",\n      });\n      form.reset({\n        title: \"\",\n        description: \"\",\n        link: \"\",\n        status: \"not-started\",\n        project: project,\n      });\n      onOpenChange(false);\n    },\n    onError: () => {\n      toast({\n        title: \"Error\", \n        description: \"Failed to create card.\",\n        variant: \"destructive\",\n      });\n    },\n  });\n\n  const onSubmit = (data: InsertCard) => {\n    createCardMutation.mutate({ ...data, project });\n  };\n\n  const getStatusLabel = (status: string) => {\n    const labels = {\n      \"not-started\": \"Not Started\",\n      \"blocked\": \"Blocked\",\n      \"in-progress\": \"In Progress\", \n      \"complete\": \"Complete\",\n      \"verified\": \"Verified\",\n    };\n    return labels[status as keyof typeof labels] || status;\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"sm:max-w-[425px]\">\n        <DialogHeader>\n          <DialogTitle>Add New Card</DialogTitle>\n        </DialogHeader>\n        \n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\n            <FormField\n              control={form.control}\n              name=\"title\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel>Title</FormLabel>\n                  <FormControl>\n                    <Input placeholder=\"Enter card title\" {...field} />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n            \n            <FormField\n              control={form.control}\n              name=\"description\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel>Description</FormLabel>\n                  <FormControl>\n                    <Textarea \n                      placeholder=\"Enter card description\"\n                      className=\"min-h-[80px]\"\n                      {...field}\n                    />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n            \n            <FormField\n              control={form.control}\n              name=\"link\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel>Link (Optional)</FormLabel>\n                  <FormControl>\n                    <Input \n                      placeholder=\"https://example.com\"\n                      type=\"url\"\n                      {...field}\n                    />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n            \n            <FormField\n              control={form.control}\n              name=\"status\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel>Status</FormLabel>\n                  <Select onValueChange={field.onChange} defaultValue={field.value}>\n                    <FormControl>\n                      <SelectTrigger>\n                        <SelectValue placeholder=\"Select status\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent>\n                      {KANBAN_STATUSES.map(status => (\n                        <SelectItem key={status} value={status}>\n                          {getStatusLabel(status)}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n            \n            <div className=\"flex gap-3 pt-4\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => onOpenChange(false)}\n                className=\"flex-1\"\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={createCardMutation.isPending}\n                className=\"flex-1\"\n              >\n                {createCardMutation.isPending ? \"Creating...\" : \"Create Card\"}\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </DialogContent>\n    </Dialog>\n  );\n}\n", "size_bytes": 6027}, "client/src/components/cards-summary.tsx": {"content": "import { useState, useEffect } from \"react\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Eye, EyeOff, Download, RefreshCw, Move } from \"lucide-react\";\nimport { useWebSocket } from \"@/hooks/use-websocket\";\nimport { queryClient } from \"@/lib/queryClient\";\nimport Draggable from \"react-draggable\";\n\ninterface CardSummary {\n  id: string;\n  title: string;\n  status: string;\n  project: string;\n  order: string;\n}\n\ninterface CardsSummaryProps {\n  selectedProject?: string;\n  className?: string;\n}\n\nconst statusColors = {\n  'not-started': 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',\n  'blocked': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',\n  'in-progress': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',\n  'complete': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',\n  'verified': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'\n};\n\nconst statusLabels = {\n  'not-started': 'Not Started',\n  'blocked': 'Blocked',\n  'in-progress': 'In Progress',\n  'complete': 'Complete',\n  'verified': 'Verified'\n};\n\n// Load saved state from localStorage\nconst loadSummaryState = () => {\n  try {\n    const savedState = localStorage.getItem('cards-summary-state');\n    if (savedState) {\n      return JSON.parse(savedState);\n    }\n  } catch (error) {\n    console.warn('Failed to load summary state:', error);\n  }\n  return { isVisible: false, position: { x: 0, y: 0 } }; // Collapsed by default\n};\n\n// Save state to localStorage\nconst saveSummaryState = (state: { isVisible: boolean; position: { x: number; y: number } }) => {\n  try {\n    localStorage.setItem('cards-summary-state', JSON.stringify(state));\n  } catch (error) {\n    console.warn('Failed to save summary state:', error);\n  }\n};\n\nexport function CardsSummary({ selectedProject, className }: CardsSummaryProps) {\n  const [summaryState, setSummaryState] = useState(loadSummaryState);\n  const { isVisible, position } = summaryState;\n  \n  const { data: summary = [], isLoading, refetch } = useQuery({\n    queryKey: ['/api/cards/summary', selectedProject],\n    queryFn: () => \n      fetch(`/api/cards/summary${selectedProject ? `?project=${encodeURIComponent(selectedProject)}` : ''}`)\n        .then(res => res.json()) as Promise<CardSummary[]>\n  });\n\n  // Listen for WebSocket updates to refresh summary\n  useWebSocket({\n    onMessage: (message: any) => {\n      if (['CARD_CREATED', 'CARD_UPDATED', 'CARD_DELETED', 'CARDS_BULK_DELETED'].includes(message.type)) {\n        queryClient.invalidateQueries({ queryKey: ['/api/cards/summary'] });\n      }\n    }\n  });\n\n  // Group cards by status\n  const groupedCards = summary.reduce((acc, card) => {\n    if (!acc[card.status]) {\n      acc[card.status] = [];\n    }\n    acc[card.status].push(card);\n    return acc;\n  }, {} as Record<string, CardSummary[]>);\n\n  // Sort cards within each status by order\n  Object.keys(groupedCards).forEach(status => {\n    groupedCards[status].sort((a, b) => parseInt(a.order) - parseInt(b.order));\n  });\n\n  const downloadMarkdown = async () => {\n    try {\n      const response = await fetch(`/api/cards/summary/markdown${selectedProject ? `?project=${encodeURIComponent(selectedProject)}` : ''}`);\n      const markdown = await response.text();\n      \n      const blob = new Blob([markdown], { type: 'text/markdown' });\n      const url = URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `cards-summary${selectedProject ? `-${selectedProject.toLowerCase().replace(/\\s+/g, '-')}` : ''}.md`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Failed to download markdown:', error);\n    }\n  };\n\n  const totalCards = summary.length;\n\n  // Update state and save to localStorage\n  const updateSummaryState = (newState: Partial<{ isVisible: boolean; position: { x: number; y: number } }>) => {\n    const updatedState = { ...summaryState, ...newState };\n    setSummaryState(updatedState);\n    saveSummaryState(updatedState);\n  };\n\n  if (!isVisible) {\n    return (\n      <Draggable\n        position={position}\n        onStop={(e, data) => updateSummaryState({ position: { x: data.x, y: data.y } })}\n      >\n        <div className={`fixed top-4 right-4 z-50 ${className}`} style={{ position: 'fixed', transform: `translate(${position.x}px, ${position.y}px)` }}>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => updateSummaryState({ isVisible: true })}\n            className=\"shadow-lg\"\n          >\n            <Eye className=\"h-4 w-4 mr-2\" />\n            Show Summary\n          </Button>\n        </div>\n      </Draggable>\n    );\n  }\n\n  return (\n    <Draggable\n      position={position}\n      onStop={(e, data) => updateSummaryState({ position: { x: data.x, y: data.y } })}\n      handle=\".drag-handle\"\n    >\n      <div className={`fixed top-4 right-4 z-50 w-80 ${className}`} style={{ position: 'fixed', transform: `translate(${position.x}px, ${position.y}px)` }}>\n        <Card className=\"shadow-lg border border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n          <CardHeader className=\"pb-3\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center gap-2\">\n                <Move className=\"h-4 w-4 text-muted-foreground cursor-move drag-handle\" />\n                <CardTitle className=\"text-lg\">\n                  Cards Summary\n                  {totalCards > 0 && (\n                    <Badge variant=\"secondary\" className=\"ml-2\">\n                      {totalCards}\n                    </Badge>\n                  )}\n                </CardTitle>\n              </div>\n              <div className=\"flex items-center gap-1\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => refetch()}\n                  disabled={isLoading}\n                  className=\"h-8 w-8 p-0\"\n                >\n                  <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />\n                </Button>\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={downloadMarkdown}\n                  className=\"h-8 w-8 p-0\"\n                >\n                  <Download className=\"h-4 w-4\" />\n                </Button>\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => updateSummaryState({ isVisible: false })}\n                  className=\"h-8 w-8 p-0\"\n                >\n                  <EyeOff className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n            {selectedProject && (\n              <p className=\"text-sm text-foreground/70 font-medium\">\n                Project: {selectedProject}\n              </p>\n            )}\n          </CardHeader>\n          <CardContent className=\"space-y-4 max-h-96 overflow-y-auto\">\n            {isLoading ? (\n              <div className=\"flex items-center justify-center py-4\">\n                <RefreshCw className=\"h-4 w-4 animate-spin mr-2\" />\n                <span className=\"text-sm text-foreground/70\">Loading...</span>\n              </div>\n            ) : totalCards === 0 ? (\n              <p className=\"text-sm text-foreground/70 text-center py-4\">\n                No cards found\n              </p>\n            ) : (\n              Object.entries(groupedCards).map(([status, cards]) => (\n                <div key={status} className=\"space-y-2\">\n                  <div className=\"flex items-center gap-2\">\n                    <Badge \n                      className={statusColors[status as keyof typeof statusColors]} \n                      variant=\"secondary\"\n                    >\n                    {statusLabels[status as keyof typeof statusLabels] || status}\n                  </Badge>\n                  <span className=\"text-xs text-foreground/70 font-medium\">\n                    {cards.length}\n                  </span>\n                </div>\n                <div className=\"space-y-1\">\n                  {cards.map((card) => (\n                    <div \n                      key={card.id}\n                      className=\"text-sm p-2 rounded border bg-muted/30 hover:bg-muted/50 transition-colors\"\n                    >\n                      <div className=\"font-medium truncate\" title={card.title}>\n                        {card.title}\n                      </div>\n                      {card.project && card.project !== selectedProject && (\n                        <div className=\"text-xs text-foreground/60 font-medium\">\n                          {card.project}\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            ))\n          )}\n        </CardContent>\n      </Card>\n      </div>\n    </Draggable>\n  );\n}", "size_bytes": 9221}, "client/src/components/create-project-dialog.tsx": {"content": "import { useState } from \"react\";\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { z } from \"zod\";\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from \"@/components/ui/form\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { apiRequest } from \"@/lib/queryClient\";\nimport { insertCardSchema } from \"@shared/schema\";\n\nconst createProjectSchema = z.object({\n  projectName: z.string().min(1, \"Project name is required\").max(50, \"Project name must be less than 50 characters\"),\n  firstCardTitle: z.string().min(1, \"First card title is required\"),\n  firstCardDescription: z.string().min(1, \"First card description is required\"),\n});\n\ntype CreateProjectForm = z.infer<typeof createProjectSchema>;\n\ninterface CreateProjectDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  onProjectCreated: (projectName: string) => void;\n}\n\nexport function CreateProjectDialog({ open, onOpenChange, onProjectCreated }: CreateProjectDialogProps) {\n  const { toast } = useToast();\n  const queryClient = useQueryClient();\n\n  const form = useForm<CreateProjectForm>({\n    resolver: zodResolver(createProjectSchema),\n    defaultValues: {\n      projectName: \"\",\n      firstCardTitle: \"\",\n      firstCardDescription: \"\",\n    },\n  });\n\n  const createProjectMutation = useMutation({\n    mutationFn: async (data: CreateProjectForm) => {\n      // Convert project name to slug format\n      const projectSlug = data.projectName\n        .toLowerCase()\n        .replace(/[^a-z0-9]/g, '-')\n        .replace(/-+/g, '-')\n        .replace(/^-|-$/g, '');\n\n      // Create the first card for this project\n      const cardData = {\n        title: data.firstCardTitle,\n        description: data.firstCardDescription,\n        project: projectSlug,\n        status: \"not-started\" as const,\n        order: \"1\",\n      };\n\n      const response = await apiRequest(\"POST\", \"/api/cards\", cardData);\n      return { project: projectSlug, card: await response.json() };\n    },\n    onSuccess: (data) => {\n      queryClient.invalidateQueries({ queryKey: [\"/api/cards\"] });\n      queryClient.invalidateQueries({ queryKey: [\"/api/projects\"] });\n      \n      toast({\n        title: \"Project created\",\n        description: `Project \"${data.project}\" has been created successfully.`,\n      });\n      \n      form.reset();\n      onOpenChange(false);\n      onProjectCreated(data.project);\n    },\n    onError: () => {\n      toast({\n        title: \"Error\",\n        description: \"Failed to create project. Please try again.\",\n        variant: \"destructive\",\n      });\n    },\n  });\n\n  const onSubmit = (data: CreateProjectForm) => {\n    createProjectMutation.mutate(data);\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"sm:max-w-[500px]\">\n        <DialogHeader>\n          <DialogTitle>Create New Project</DialogTitle>\n          <DialogDescription>\n            Create a new project with your first task card. The project name will be formatted automatically.\n          </DialogDescription>\n        </DialogHeader>\n        \n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\n            <FormField\n              control={form.control}\n              name=\"projectName\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel>Project Name</FormLabel>\n                  <FormControl>\n                    <Input\n                      placeholder=\"e.g., Mobile Shopping App\"\n                      {...field}\n                    />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"space-y-3\">\n              <h4 className=\"text-sm font-medium text-gray-900\">First Task Card</h4>\n              \n              <FormField\n                control={form.control}\n                name=\"firstCardTitle\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Card Title</FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"e.g., Set up project structure\"\n                        {...field}\n                      />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={form.control}\n                name=\"firstCardDescription\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Card Description</FormLabel>\n                    <FormControl>\n                      <Input\n                        placeholder=\"e.g., Initialize the project with basic folder structure and dependencies\"\n                        {...field}\n                      />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            <div className=\"flex justify-end space-x-2 pt-4\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => onOpenChange(false)}\n                disabled={createProjectMutation.isPending}\n              >\n                Cancel\n              </Button>\n              <Button\n                type=\"submit\"\n                disabled={createProjectMutation.isPending}\n                className=\"bg-blue-600 hover:bg-blue-700\"\n              >\n                {createProjectMutation.isPending ? \"Creating...\" : \"Create Project\"}\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </DialogContent>\n    </Dialog>\n  );\n}", "size_bytes": 6074}, "client/src/components/edit-card-dialog.tsx": {"content": "import { useState } from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\nimport { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from \"@/components/ui/dialog\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from \"@/components/ui/form\";\nimport { Card, updateCardSchema, UpdateCard, KANBAN_STATUSES, KanbanStatus } from \"@shared/schema\";\nimport { apiRequest } from \"@/lib/queryClient\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { z } from \"zod\";\n\n// Extended schema for editing (includes status field)\nconst editCardSchema = updateCardSchema.extend({\n  title: z.string().min(1, \"Title is required\"),\n  description: z.string().min(1, \"Description is required\"),\n  status: z.enum(KANBAN_STATUSES),\n  link: z.string().url().optional().or(z.literal(\"\")),\n});\n\ntype EditCardForm = z.infer<typeof editCardSchema>;\n\ninterface EditCardDialogProps {\n  card: Card;\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n}\n\nexport function EditCardDialog({ card, open, onOpenChange }: EditCardDialogProps) {\n  const { toast } = useToast();\n  const queryClient = useQueryClient();\n\n  const form = useForm<EditCardForm>({\n    resolver: zodResolver(editCardSchema),\n    defaultValues: {\n      title: card.title,\n      description: card.description,\n      link: card.link || \"\",\n      status: card.status as KanbanStatus,\n    },\n  });\n\n  const updateMutation = useMutation({\n    mutationFn: async (data: EditCardForm) => {\n      const updates: UpdateCard = {\n        ...data,\n        link: data.link || null,\n      };\n      const response = await apiRequest(\"PATCH\", `/api/cards/${card.id}`, updates);\n      return response.json();\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: [\"/api/cards\"] });\n      toast({\n        title: \"Card updated\",\n        description: \"Your changes have been saved successfully.\",\n      });\n      onOpenChange(false);\n      form.reset();\n    },\n    onError: () => {\n      toast({\n        title: \"Error\",\n        description: \"Failed to update card. Please try again.\",\n        variant: \"destructive\",\n      });\n    },\n  });\n\n  const onSubmit = (data: EditCardForm) => {\n    updateMutation.mutate(data);\n  };\n\n  const getStatusLabel = (status: KanbanStatus) => {\n    const labels = {\n      \"not-started\": \"Not Started\",\n      \"blocked\": \"Blocked\",\n      \"in-progress\": \"In Progress\",\n      \"complete\": \"Complete\",\n      \"verified\": \"Verified\"\n    };\n    return labels[status];\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"sm:max-w-[600px] bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 shadow-2xl\">\n        <DialogHeader>\n          <DialogTitle>Edit Card</DialogTitle>\n          <DialogDescription>\n            Modify the card details below. All fields support rich formatting.\n          </DialogDescription>\n        </DialogHeader>\n        \n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\n            <FormField\n              control={form.control}\n              name=\"title\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel>Title</FormLabel>\n                  <FormControl>\n                    <Input \n                      placeholder=\"Enter card title\" \n                      className=\"bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600\"\n                      {...field} \n                    />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n            \n            <FormField\n              control={form.control}\n              name=\"description\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel>Description</FormLabel>\n                  <FormControl>\n                    <Textarea \n                      placeholder=\"Enter card description (Markdown supported)\"\n                      className=\"min-h-[150px] resize-y bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600\"\n                      {...field}\n                    />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n            \n            <FormField\n              control={form.control}\n              name=\"link\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel>Link (Optional)</FormLabel>\n                  <FormControl>\n                    <Input \n                      placeholder=\"https://example.com\" \n                      type=\"url\"\n                      className=\"bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600\"\n                      {...field}\n                    />\n                  </FormControl>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n\n            <FormField\n              control={form.control}\n              name=\"status\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel>Status</FormLabel>\n                  <Select onValueChange={field.onChange} defaultValue={field.value}>\n                    <FormControl>\n                      <SelectTrigger className=\"bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600\">\n                        <SelectValue placeholder=\"Select status\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent>\n                      {KANBAN_STATUSES.map((status) => (\n                        <SelectItem key={status} value={status}>\n                          {getStatusLabel(status)}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n            \n            <div className=\"flex justify-end space-x-2 pt-6 border-t border-gray-200 dark:border-gray-700 mt-4\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => onOpenChange(false)}\n                className=\"bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700\"\n              >\n                Cancel\n              </Button>\n              <Button \n                type=\"submit\" \n                disabled={updateMutation.isPending}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white\"\n              >\n                {updateMutation.isPending ? \"Updating...\" : \"Update Card\"}\n              </Button>\n            </div>\n          </form>\n        </Form>\n      </DialogContent>\n    </Dialog>\n  );\n}", "size_bytes": 7230}, "client/src/components/explosion-animation.tsx": {"content": "import { motion } from \"framer-motion\";\nimport { useEffect, useState } from \"react\";\n\ninterface ExplosionAnimationProps {\n  trigger: boolean;\n  onComplete: () => void;\n  children: React.ReactNode;\n}\n\nexport function ExplosionAnimation({ trigger, onComplete, children }: ExplosionAnimationProps) {\n  const [isExploding, setIsExploding] = useState(false);\n\n  useEffect(() => {\n    if (trigger) {\n      setIsExploding(true);\n    }\n  }, [trigger]);\n\n  if (!isExploding) {\n    return <>{children}</>;\n  }\n\n  // Create explosion particles\n  const particles = Array.from({ length: 12 }, (_, i) => {\n    const angle = (i * 30) * (Math.PI / 180); // Convert to radians\n    const distance = 100 + Math.random() * 50;\n    const x = Math.cos(angle) * distance;\n    const y = Math.sin(angle) * distance;\n    \n    return { x, y, delay: Math.random() * 0.1 };\n  });\n\n  return (\n    <motion.div\n      className=\"relative\"\n      initial={{ scale: 1, rotate: 0 }}\n      animate={{ \n        scale: [1, 1.1, 0.8, 0],\n        rotate: [0, -5, 5, 0],\n        opacity: [1, 1, 0.8, 0]\n      }}\n      transition={{ \n        duration: 0.6,\n        ease: \"easeOut\"\n      }}\n      onAnimationComplete={onComplete}\n    >\n      {children}\n      \n      {/* Explosion particles */}\n      {particles.map((particle, i) => (\n        <motion.div\n          key={i}\n          className=\"absolute top-1/2 left-1/2 w-2 h-2 bg-gradient-to-r from-orange-400 to-red-500 rounded-full\"\n          initial={{ \n            x: 0, \n            y: 0, \n            scale: 0,\n            opacity: 1\n          }}\n          animate={{ \n            x: particle.x,\n            y: particle.y,\n            scale: [0, 1, 0.5, 0],\n            opacity: [1, 1, 0.8, 0]\n          }}\n          transition={{ \n            duration: 0.8,\n            delay: particle.delay,\n            ease: \"easeOut\"\n          }}\n        />\n      ))}\n      \n      {/* Central flash */}\n      <motion.div\n        className=\"absolute inset-0 bg-white rounded-lg\"\n        initial={{ opacity: 0, scale: 0.8 }}\n        animate={{ \n          opacity: [0, 0.8, 0],\n          scale: [0.8, 1.2, 1.5]\n        }}\n        transition={{ \n          duration: 0.3,\n          ease: \"easeOut\"\n        }}\n      />\n    </motion.div>\n  );\n}", "size_bytes": 2236}, "client/src/components/interactive-markdown.tsx": {"content": "import React from 'react';\nimport ReactMarkdown from 'react-markdown';\nimport remarkGfm from 'remark-gfm';\nimport { cn } from '@/lib/utils';\nimport { TaskItem } from '@/lib/task-utils';\n\ninterface InteractiveMarkdownProps {\n  content: string;\n  tasks: TaskItem[];\n  onTaskToggle: (taskText: string, completed: boolean) => void;\n  isExpanded: boolean;\n}\n\nexport function InteractiveMarkdown({ content, tasks, onTaskToggle, isExpanded }: InteractiveMarkdownProps) {\n  return (\n    <div className={cn(\n      \"text-sm text-gray-600 break-words prose prose-sm max-w-none\",\n      !isExpanded && \"line-clamp-2\"\n    )}>\n      <ReactMarkdown \n        remarkPlugins={[remarkGfm]}\n        components={{\n          // Override styles for better card appearance\n          p: ({ children }) => <p className=\"mb-2 last:mb-0 text-gray-700 dark:text-gray-300\">{children}</p>,\n          ul: ({ children }) => <ul className=\"mb-2 last:mb-0 ml-2\">{children}</ul>,\n          ol: ({ children }) => <ol className=\"mb-2 last:mb-0 ml-4\">{children}</ol>,\n          li: ({ children, node }) => {\n            // Check if this is a task list item with remarkGfm\n            const isTaskListItem = Array.isArray(node?.properties?.className) \n              ? node.properties.className.includes('task-list-item')\n              : typeof node?.properties?.className === 'string' \n              ? node.properties.className.includes('task-list-item')\n              : false;\n            \n            if (isTaskListItem) {\n              // Extract the text content from children\n              const textContent = extractTextFromChildren(children);\n              const taskText = textContent.replace(/^\\[([x\\s])\\]\\s*/, '').trim();\n              const task = tasks.find(t => t.text === taskText);\n              \n              return (\n                <li className=\"mb-1 flex items-center gap-1.5 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors task-list-item\">\n                  <input\n                    type=\"checkbox\"\n                    checked={task?.completed ?? false}\n                    onChange={(e) => {\n                      onTaskToggle(taskText, e.target.checked);\n                    }}\n                    className=\"w-3.5 h-3.5 accent-blue-500 dark:accent-blue-400 rounded flex-shrink-0\"\n                    onClick={(e) => e.stopPropagation()}\n                  />\n                  <span className={cn(\n                    \"text-xs leading-4 flex-1\",\n                    task?.completed ? \"line-through text-gray-500 dark:text-gray-400\" : \"text-gray-700 dark:text-gray-300\"\n                  )}>\n                    {taskText}\n                  </span>\n                </li>\n              );\n            }\n            return <li className=\"mb-1 ml-4 text-xs text-gray-700 dark:text-gray-300\">{children}</li>;\n          },\n          code: ({ children }) => <code className=\"bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded text-xs text-gray-800 dark:text-gray-200\">{children}</code>,\n          strong: ({ children }) => <strong className=\"font-semibold text-gray-800 dark:text-gray-200\">{children}</strong>,\n          em: ({ children }) => <em className=\"italic text-gray-700 dark:text-gray-300\">{children}</em>,\n          h1: ({ children }) => <h1 className=\"text-base font-semibold mb-1\">{children}</h1>,\n          h2: ({ children }) => <h2 className=\"text-sm font-semibold mb-1\">{children}</h2>,\n          h3: ({ children }) => <h3 className=\"text-sm font-medium mb-1\">{children}</h3>,\n          blockquote: ({ children }) => <blockquote className=\"border-l-2 border-gray-300 pl-2 italic\">{children}</blockquote>,\n          a: ({ href, children }) => (\n            <a \n              href={href} \n              target=\"_blank\" \n              rel=\"noopener noreferrer\" \n              className=\"text-blue-600 hover:text-blue-800 underline\"\n              onClick={(e) => e.stopPropagation()}\n            >\n              {children}\n            </a>\n          ),\n        }}\n      >\n        {content}\n      </ReactMarkdown>\n    </div>\n  );\n}\n\nfunction extractTextFromChildren(children: React.ReactNode): string {\n  if (typeof children === 'string') {\n    return children;\n  }\n  if (Array.isArray(children)) {\n    return children.map(extractTextFromChildren).join('');\n  }\n  if (React.isValidElement(children)) {\n    return extractTextFromChildren(children.props.children);\n  }\n  return '';\n}", "size_bytes": 4380}, "client/src/components/kanban-board.tsx": {"content": "import { useState, useEffect } from \"react\";\nimport { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\";\nimport { DndContext, DragEndEvent, DragOverlay, DragStartEvent, rectIntersection, PointerSensor, useSensor, useSensors, pointerWithin } from \"@dnd-kit/core\";\nimport { arrayMove, SortableContext, verticalListSortingStrategy } from \"@dnd-kit/sortable\";\nimport { motion, LayoutGroup } from \"framer-motion\";\n\nimport { KanbanColumn } from \"./kanban-column\";\nimport { TaskCard } from \"./task-card\";\nimport { AddCardDialog } from \"./add-card-dialog\";\n\nimport { EditCardDialog } from \"./edit-card-dialog\";\nimport { Button } from \"@/components/ui/button\";\n\nimport { Plus, Filter, RotateCcw } from \"lucide-react\";\n\nimport { Card, KANBAN_STATUSES, KanbanStatus } from \"@shared/schema\";\nimport { apiRequest } from \"@/lib/queryClient\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { useWebSocket } from \"@/hooks/use-websocket\";\nimport { cn } from \"@/lib/utils\";\n\n// Default column widths\nconst DEFAULT_COLUMN_WIDTHS: Record<KanbanStatus, number> = {\n  \"not-started\": 320,\n  \"blocked\": 320,\n  \"in-progress\": 320,\n  \"complete\": 320,\n  \"verified\": 320,\n};\n\n// Load saved column widths from localStorage\nconst loadColumnWidths = (): Record<KanbanStatus, number> => {\n  try {\n    const saved = localStorage.getItem('kanban-column-widths');\n    if (saved) {\n      const parsed = JSON.parse(saved);\n      // Validate that all required statuses are present and values are numbers\n      const isValid = KANBAN_STATUSES.every(status => \n        typeof parsed[status] === 'number' && \n        parsed[status] >= 280 && \n        parsed[status] <= 600\n      );\n      if (isValid) {\n        return parsed;\n      }\n    }\n  } catch (error) {\n    console.warn('Failed to load column widths from localStorage:', error);\n  }\n  return DEFAULT_COLUMN_WIDTHS;\n};\n\n// Save column widths to localStorage\nconst saveColumnWidths = (widths: Record<KanbanStatus, number>) => {\n  try {\n    localStorage.setItem('kanban-column-widths', JSON.stringify(widths));\n  } catch (error) {\n    console.warn('Failed to save column widths to localStorage:', error);\n  }\n};\n\ninterface KanbanBoardProps {\n  selectedProject?: string;\n}\n\nexport function KanbanBoard({ selectedProject }: KanbanBoardProps) {\n  const [activeCard, setActiveCard] = useState<Card | null>(null);\n  const [showAddDialog, setShowAddDialog] = useState(false);\n\n  const [showEditDialog, setShowEditDialog] = useState(false);\n  const [editingCard, setEditingCard] = useState<Card | null>(null);\n  const [columnWidths, setColumnWidths] = useState<Record<KanbanStatus, number>>(loadColumnWidths);\n  const { toast } = useToast();\n  const queryClient = useQueryClient();\n  const { isConnected } = useWebSocket();\n  \n  // Use the selectedProject passed as prop\n  \n  // Handle column width changes\n  const handleColumnWidthChange = (status: KanbanStatus, newWidth: number) => {\n    const updatedWidths = {\n      ...columnWidths,\n      [status]: newWidth\n    };\n    setColumnWidths(updatedWidths);\n    saveColumnWidths(updatedWidths);\n  };\n\n  // Reset all column widths to defaults\n  const resetColumnWidths = () => {\n    setColumnWidths(DEFAULT_COLUMN_WIDTHS);\n    saveColumnWidths(DEFAULT_COLUMN_WIDTHS);\n    toast({\n      title: \"Column widths reset\",\n      description: \"All columns have been reset to default width.\",\n    });\n  };\n\n  // Handle card editing\n  const handleEditCard = (card: Card) => {\n    setEditingCard(card);\n    setShowEditDialog(true);\n  };\n  \n  const sensors = useSensors(\n    useSensor(PointerSensor, {\n      activationConstraint: {\n        distance: 8,\n      },\n    })\n  );\n\n  const { data: cards = [], isLoading } = useQuery<Card[]>({\n    queryKey: [\"/api/cards\", selectedProject],\n    queryFn: async () => {\n      const url = selectedProject \n        ? `/api/cards?project=${encodeURIComponent(selectedProject)}`\n        : '/api/cards';\n      const response = await fetch(url);\n      if (!response.ok) throw new Error('Failed to fetch cards');\n      return response.json();\n    },\n  });\n\n  const updateCardMutation = useMutation({\n    mutationFn: async ({ id, updates }: { id: string; updates: Partial<Card> }) => {\n      const response = await apiRequest(\"PATCH\", `/api/cards/${id}`, updates);\n      return response.json();\n    },\n    onSuccess: () => {\n      // Don't invalidate immediately for drag operations to avoid flicker\n      // The optimistic update handles the UI, server response confirms it\n    },\n    onError: () => {\n      // Revert optimistic update on error\n      queryClient.invalidateQueries({ queryKey: [\"/api/cards\", selectedProject] });\n      toast({\n        title: \"Error\",\n        description: \"Failed to update card. Changes reverted.\",\n        variant: \"destructive\",\n      });\n    },\n  });\n\n  const getCardsByStatus = (status: KanbanStatus) => {\n    return cards\n      .filter(card => card.status === status)\n      .sort((a, b) => parseInt(a.order) - parseInt(b.order));\n  };\n\n  const getColumnConfig = (status: KanbanStatus) => {\n    const configs = {\n      \"not-started\": { title: \"Not Started\", color: \"gray\", bgColor: \"bg-gradient-to-r from-gray-50 to-slate-50\" },\n      \"blocked\": { title: \"Blocked\", color: \"red\", bgColor: \"bg-gradient-to-r from-red-50 to-rose-50\" },\n      \"in-progress\": { title: \"In Progress\", color: \"blue\", bgColor: \"bg-gradient-to-r from-blue-50 to-indigo-50\" },\n      \"complete\": { title: \"Complete\", color: \"green\", bgColor: \"bg-gradient-to-r from-green-50 to-emerald-50\" },\n      \"verified\": { title: \"Verified\", color: \"purple\", bgColor: \"bg-gradient-to-r from-purple-50 to-violet-50\" },\n    };\n    return configs[status];\n  };\n\n  const handleDragStart = (event: DragStartEvent) => {\n    const card = cards.find(c => c.id === event.active.id);\n    setActiveCard(card || null);\n  };\n\n  const handleDragEnd = (event: DragEndEvent) => {\n    const { active, over } = event;\n    \n    if (!over || !activeCard) {\n      setActiveCard(null);\n      return;\n    }\n\n    const activeId = active.id.toString();\n    const overId = over.id.toString();\n    \n    // Immediately apply optimistic updates for smoother UX\n    const optimisticUpdate = () => {\n      // If we're dropping on a column (status ID)\n      if (KANBAN_STATUSES.includes(overId as KanbanStatus)) {\n        const newStatus = overId as KanbanStatus;\n        if (activeCard.status !== newStatus) {\n          // Optimistically move card to new column\n          const updatedCards = cards.map(card => \n            card.id === activeCard.id \n              ? { ...card, status: newStatus }\n              : card\n          );\n          queryClient.setQueryData([\"/api/cards\", selectedProject], updatedCards);\n          \n          updateCardMutation.mutate({\n            id: activeCard.id,\n            updates: { status: newStatus }\n          });\n        }\n      } else {\n        // We're dropping on a card - check if it's the same status or different\n        const overCard = cards.find(card => card.id === overId);\n        \n        if (overCard) {\n          if (activeCard.status === overCard.status) {\n            // Same column reordering\n            const statusCards = getCardsByStatus(activeCard.status as KanbanStatus);\n            const activeIndex = statusCards.findIndex(card => card.id === activeId);\n            const overIndex = statusCards.findIndex(card => card.id === overId);\n            \n            if (activeIndex !== overIndex && activeIndex !== -1 && overIndex !== -1) {\n              const reorderedCards = arrayMove(statusCards, activeIndex, overIndex);\n              \n              // Update the order for all cards in this status\n              const allCardsWithNewOrder = cards.map(card => {\n                if (card.status === activeCard.status) {\n                  const reorderedIndex = reorderedCards.findIndex(c => c.id === card.id);\n                  if (reorderedIndex !== -1) {\n                    return { ...card, order: (reorderedIndex + 1).toString() };\n                  }\n                }\n                return card;\n              });\n              \n              // Optimistic update\n              queryClient.setQueryData([\"/api/cards\", selectedProject], allCardsWithNewOrder);\n              \n              // Update all affected cards' orders through batch API\n              const orderUpdates = reorderedCards.map((card, index) => ({\n                id: card.id,\n                updates: { order: (index + 1).toString() }\n              }));\n              \n              // For now, just update the moved card - we could add batch update later\n              const activeCardNewIndex = reorderedCards.findIndex(c => c.id === activeCard.id);\n              updateCardMutation.mutate({\n                id: activeCard.id,\n                updates: { order: (activeCardNewIndex + 1).toString() }\n              });\n            }\n          } else {\n            // Different column - move to that column\n            const updatedCards = cards.map(card => \n              card.id === activeCard.id \n                ? { ...card, status: overCard.status }\n                : card\n            );\n            queryClient.setQueryData([\"/api/cards\", selectedProject], updatedCards);\n            \n            updateCardMutation.mutate({\n              id: activeCard.id,\n              updates: { status: overCard.status }\n            });\n          }\n        }\n      }\n    };\n\n    // Apply optimistic update immediately\n    optimisticUpdate();\n    \n    // Clear active card immediately since we're using optimistic updates\n    setActiveCard(null);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"w-full overflow-x-auto\">\n        <div className=\"flex gap-6 min-h-screen pb-4 px-6\">\n          {KANBAN_STATUSES.map(status => (\n            <div key={status} className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4\" style={{ width: DEFAULT_COLUMN_WIDTHS[status], minWidth: DEFAULT_COLUMN_WIDTHS[status] }}>\n              <div className=\"animate-pulse\">\n                <div className=\"h-6 bg-gray-300 dark:bg-gray-600 rounded mb-4\"></div>\n                <div className=\"space-y-3\">\n                  <div className=\"h-20 bg-gray-200 dark:bg-gray-700 rounded\"></div>\n                  <div className=\"h-20 bg-gray-200 dark:bg-gray-700 rounded\"></div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <div className=\"flex items-center justify-between mb-6 max-w-7xl mx-auto px-6\">\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex items-center space-x-2\">\n            <div className={cn(\n              \"w-2 h-2 rounded-full\",\n              isConnected ? \"bg-green-500\" : \"bg-red-500\"\n            )}></div>\n            <span className=\"text-sm text-gray-600 dark:text-gray-300\">\n              {isConnected ? \"Real-time updates active\" : \"Connecting...\"}\n            </span>\n          </div>\n          \n\n        </div>\n        \n        <div className=\"flex items-center space-x-3\">\n          <Button\n            onClick={() => setShowAddDialog(true)}\n            className=\"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 dark:from-blue-500 dark:to-blue-600 dark:hover:from-blue-600 dark:hover:to-blue-700 text-white font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105\"\n          >\n            <Plus className=\"w-4 h-4 mr-2\" />\n            Add Card\n          </Button>\n          <Button \n            variant=\"outline\" \n            onClick={resetColumnWidths}\n            className=\"bg-gradient-to-r from-orange-100 to-orange-200 hover:from-orange-200 hover:to-orange-300 dark:from-orange-700 dark:to-orange-600 dark:hover:from-orange-600 dark:hover:to-orange-500 text-orange-700 dark:text-orange-200 border-orange-300 dark:border-orange-600 shadow-md hover:shadow-lg transition-all duration-300\"\n            title=\"Reset column widths to default\"\n          >\n            <RotateCcw className=\"w-4 h-4\" />\n          </Button>\n          <Button variant=\"outline\" className=\"bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 dark:from-gray-700 dark:to-gray-600 dark:hover:from-gray-600 dark:hover:to-gray-500 text-gray-700 dark:text-gray-200 border-gray-300 dark:border-gray-600 shadow-md hover:shadow-lg transition-all duration-300\">\n            <Filter className=\"w-4 h-4\" />\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"w-full overflow-x-auto\">\n        <LayoutGroup>\n          <DndContext\n            sensors={sensors}\n            onDragStart={handleDragStart}\n            onDragEnd={handleDragEnd}\n            collisionDetection={pointerWithin}\n          >\n            <div className=\"flex gap-6 min-h-screen pb-4 px-6\">\n              {KANBAN_STATUSES.map(status => {\n                const config = getColumnConfig(status);\n                const columnCards = getCardsByStatus(status);\n                \n                return (\n                  <KanbanColumn\n                    key={status}\n                    id={status}\n                    title={config.title}\n                    color={config.color}\n                    bgColor={config.bgColor}\n                    cards={columnCards}\n                    count={columnCards.length}\n                    width={columnWidths[status]}\n                    onWidthChange={(newWidth) => handleColumnWidthChange(status, newWidth)}\n                    onEditCard={handleEditCard}\n                  />\n                );\n              })}\n            </div>\n        \n        <DragOverlay dropAnimation={null}>\n          {activeCard ? (\n            <motion.div\n              initial={{ rotate: 2, scale: 1.05, opacity: 0.9 }}\n              animate={{ rotate: 2, scale: 1.05, opacity: 0.9 }}\n              exit={{ scale: 1, rotate: 0, opacity: 1 }}\n              transition={{ duration: 0.2, ease: \"easeInOut\" }}\n              className=\"shadow-2xl\"\n            >\n              <TaskCard card={activeCard} />\n            </motion.div>\n          ) : null}\n        </DragOverlay>\n          </DndContext>\n        </LayoutGroup>\n      </div>\n\n      <AddCardDialog\n        open={showAddDialog}\n        onOpenChange={setShowAddDialog}\n        project={selectedProject || \"\"}\n      />\n\n      {editingCard && (\n        <EditCardDialog\n          card={editingCard}\n          open={showEditDialog}\n          onOpenChange={(open) => {\n            setShowEditDialog(open);\n            if (!open) {\n              setEditingCard(null);\n            }\n          }}\n        />\n      )}\n    </>\n  );\n}\n", "size_bytes": 14638}, "client/src/components/kanban-column.tsx": {"content": "import { useDroppable } from \"@dnd-kit/core\";\nimport { SortableContext, verticalListSortingStrategy } from \"@dnd-kit/sortable\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { TaskCard } from \"./task-card\";\nimport { Card, KanbanStatus } from \"@shared/schema\";\nimport { cn } from \"@/lib/utils\";\nimport { GripVertical } from \"lucide-react\";\nimport { useState, useRef } from \"react\";\n\ninterface KanbanColumnProps {\n  id: KanbanStatus;\n  title: string;\n  color: string;\n  bgColor: string;\n  cards: Card[];\n  count: number;\n  width?: number;\n  onWidthChange?: (width: number) => void;\n  onEditCard?: (card: Card) => void;\n}\n\nexport function KanbanColumn({ id, title, color, bgColor, cards, count, width = 320, onWidthChange, onEditCard }: KanbanColumnProps) {\n  const { setNodeRef, isOver } = useDroppable({\n    id: id,\n  });\n  \n  const [isResizing, setIsResizing] = useState(false);\n  const resizeRef = useRef<HTMLDivElement>(null);\n  const startXRef = useRef(0);\n  const startWidthRef = useRef(0);\n\n  const getStatusDotColor = () => {\n    const colors = {\n      gray: \"bg-gradient-to-r from-gray-400 to-gray-500 shadow-md\",\n      red: \"bg-gradient-to-r from-red-400 to-red-500 shadow-md\", \n      blue: \"bg-gradient-to-r from-blue-400 to-blue-500 shadow-md\",\n      green: \"bg-gradient-to-r from-green-400 to-green-500 shadow-md\",\n      purple: \"bg-gradient-to-r from-purple-400 to-purple-500 shadow-md\",\n    };\n    return colors[color as keyof typeof colors] || \"bg-gradient-to-r from-gray-400 to-gray-500 shadow-md\";\n  };\n\n  const getCountBadgeColor = () => {\n    const colors = {\n      gray: \"bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 shadow-sm\",\n      red: \"bg-gradient-to-r from-red-100 to-red-200 text-red-700 shadow-sm\",\n      blue: \"bg-gradient-to-r from-blue-100 to-blue-200 text-blue-700 shadow-sm\", \n      green: \"bg-gradient-to-r from-green-100 to-green-200 text-green-700 shadow-sm\",\n      purple: \"bg-gradient-to-r from-purple-100 to-purple-200 text-purple-700 shadow-sm\",\n    };\n    return colors[color as keyof typeof colors] || \"bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 shadow-sm\";\n  };\n\n  const getHeaderGradient = () => {\n    const gradients = {\n      gray: \"bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700\",\n      red: \"bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/30 dark:to-red-800/30\",\n      blue: \"bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/30\",\n      green: \"bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/30\",\n      purple: \"bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/30 dark:to-purple-800/30\",\n    };\n    return gradients[color as keyof typeof gradients] || \"bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700\";\n  };\n\n  const handleMouseDown = (e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setIsResizing(true);\n    startXRef.current = e.clientX;\n    startWidthRef.current = width;\n    \n    const handleMouseMove = (e: MouseEvent) => {\n      const deltaX = e.clientX - startXRef.current;\n      const newWidth = Math.max(280, Math.min(600, startWidthRef.current + deltaX));\n      \n      if (onWidthChange) {\n        onWidthChange(newWidth);\n      }\n    };\n    \n    const handleMouseUp = () => {\n      setIsResizing(false);\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleMouseUp);\n    };\n    \n    document.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseup', handleMouseUp);\n  };\n\n  return (\n    <div \n      ref={setNodeRef}\n      className={cn(\n        \"kanban-column rounded-xl overflow-hidden flex-shrink-0 relative group min-h-full transition-all duration-300 ease-in-out\",\n        isOver && \"ring-2 ring-blue-400 ring-opacity-60 shadow-xl scale-[1.02]\"\n      )}\n      style={{ width: `${width}px` }}\n    >\n      <div className=\"min-h-full\">\n        <div className={cn(\"px-5 py-4 border-b border-white/20 dark:border-gray-600/20\", getHeaderGradient())}>\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className={cn(\"w-4 h-4 rounded-full\", getStatusDotColor())}></div>\n              <h3 className=\"font-bold text-gray-800 dark:text-gray-200 text-sm tracking-wide uppercase\">{title}</h3>\n            </div>\n            <span className={cn(\"text-xs px-3 py-1.5 rounded-full font-bold\", getCountBadgeColor())}>\n              {count}\n            </span>\n          </div>\n        </div>\n        \n        <div\n          className={cn(\n            \"p-5 min-h-[500px] transition-all duration-300 bg-white/50 dark:bg-gray-800/50\",\n            isOver && \"bg-gradient-to-b from-blue-50/50 to-blue-100/30 dark:from-blue-900/30 dark:to-blue-800/20\"\n          )}\n        >\n          <SortableContext items={cards.map(card => card.id)} strategy={verticalListSortingStrategy}>\n            <motion.div className=\"space-y-3\" layout>\n              <AnimatePresence mode=\"popLayout\">\n                {cards.map(card => (\n                  <motion.div\n                    key={card.id}\n                    layoutId={`card-${card.id}`}\n                    initial={{ opacity: 0, scale: 0.8 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    exit={{ opacity: 0, scale: 0.8 }}\n                    transition={{ \n                      layout: { duration: 0.5, ease: \"easeInOut\" },\n                      opacity: { duration: 0.3 },\n                      scale: { duration: 0.3 }\n                    }}\n                  >\n                    <TaskCard card={card} onEdit={onEditCard} />\n                  </motion.div>\n                ))}\n              </AnimatePresence>\n            </motion.div>\n          </SortableContext>\n          \n          {/* Drop zone for empty columns - make it more prominent when empty */}\n          {cards.length === 0 ? (\n            <div className={cn(\n              \"h-40 w-full border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center transition-all duration-200\",\n              isOver && \"border-blue-400 bg-blue-50/50 dark:bg-blue-900/20 border-solid\"\n            )}>\n              <p className=\"text-gray-500 dark:text-gray-400 text-sm font-medium\">\n                Drop cards here\n              </p>\n            </div>\n          ) : (\n            <div className=\"h-16 w-full\" />\n          )}\n        </div>\n      </div>\n      \n      {/* Resize handle */}\n      <div\n        ref={resizeRef}\n        onMouseDown={handleMouseDown}\n        className={cn(\n          \"absolute top-0 right-0 w-3 h-full cursor-col-resize opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center z-10 bg-transparent hover:bg-blue-50/20\",\n          isResizing && \"opacity-100 bg-blue-50/30\"\n        )}\n        title=\"Drag to resize column\"\n      >\n        <div className=\"w-1 h-12 bg-gray-400 dark:bg-gray-500 rounded-full hover:bg-blue-500 dark:hover:bg-blue-400 transition-colors shadow-sm\"></div>\n      </div>\n    </div>\n  );\n}\n", "size_bytes": 7208}, "client/src/components/task-card.tsx": {"content": "import { useSortable } from \"@dnd-kit/sortable\";\nimport { CSS } from \"@dnd-kit/utilities\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { GripVertical, ExternalLink, CheckCircle, AlertTriangle, Shield, Trash2, ChevronDown, ChevronUp, Edit<PERSON>, <PERSON><PERSON>, <PERSON>, Eye } from \"lucide-react\";\nimport { Card, KanbanStatus } from \"@shared/schema\";\nimport { cn } from \"@/lib/utils\";\nimport { ExplosionAnimation } from \"./explosion-animation\";\nimport { parseTaskList, calculateTaskProgress, updateTaskCompletion, hasTaskList, serializeTaskList, extractTasksFromMarkdown, TaskItem } from \"@/lib/task-utils\";\nimport { InteractiveMarkdown } from \"./interactive-markdown\";\nimport { ViewCardDialog } from \"./view-card-dialog\";\nimport { useState } from \"react\";\nimport { useMutation, useQueryClient } from \"@tanstack/react-query\";\nimport { apiRequest } from \"@/lib/queryClient\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport ReactMarkdown from \"react-markdown\";\nimport remarkGfm from \"remark-gfm\";\n\ninterface TaskCardProps {\n  card: Card & { \n    _remoteUpdate?: boolean; \n    _statusChanged?: boolean; \n  };\n  onEdit?: (card: Card) => void;\n}\n\nexport function TaskCard({ card, onEdit }: TaskCardProps) {\n  const [isExploding, setIsExploding] = useState(false);\n  const [shouldHide, setShouldHide] = useState(false);\n  const [isExpanded, setIsExpanded] = useState(card.status === \"in-progress\");\n  const [justCopied, setJustCopied] = useState(false);\n  const [showViewDialog, setShowViewDialog] = useState(false);\n  const queryClient = useQueryClient();\n  const { toast } = useToast();\n\n  // Extract tasks from markdown and sync with stored task list\n  const markdownTasks = extractTasksFromMarkdown(card.description);\n  const storedTasks = parseTaskList(card.taskList);\n  \n  // Merge markdown tasks with stored completion status, preferring stored state\n  const tasks = markdownTasks.map(markdownTask => {\n    const storedTask = storedTasks.find(stored => stored.text === markdownTask.text);\n    return storedTask || markdownTask;\n  });\n  \n  const taskProgress = calculateTaskProgress(tasks);\n  const showProgress = tasks.length > 0;\n\n  const {\n    attributes,\n    listeners,\n    setNodeRef,\n    transform,\n    transition,\n    isDragging,\n  } = useSortable({ id: card.id });\n\n  const deleteMutation = useMutation({\n    mutationFn: () => apiRequest(\"DELETE\", `/api/cards/${card.id}`),\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: [\"/api/cards\"] });\n      toast({\n        title: \"Card deleted\",\n        description: \"The card has been successfully removed.\",\n      });\n    },\n    onError: () => {\n      toast({\n        title: \"Error\",\n        description: \"Failed to delete card. Please try again.\",\n        variant: \"destructive\",\n      });\n      // Reset animation state on error\n      setIsExploding(false);\n      setShouldHide(false);\n    },\n  });\n\n  const updateTaskMutation = useMutation({\n    mutationFn: async (updatedTasks: TaskItem[]) => {\n      const response = await apiRequest(\"PATCH\", `/api/cards/${card.id}`, {\n        taskList: serializeTaskList(updatedTasks)\n      });\n      return response.json();\n    },\n    onSuccess: () => {\n      queryClient.invalidateQueries({ queryKey: [\"/api/cards\"] });\n    },\n    onError: () => {\n      toast({\n        title: \"Error\",\n        description: \"Failed to update task progress.\",\n        variant: \"destructive\",\n      });\n    },\n  });\n\n  const handleDelete = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    setIsExploding(true);\n  };\n\n  const handleExplosionComplete = () => {\n    setShouldHide(true);\n    deleteMutation.mutate();\n  };\n\n  const handleTaskToggle = (taskText: string, completed: boolean) => {\n    const updatedTasks = tasks.map(task => \n      task.text === taskText ? { ...task, completed } : task\n    );\n    updateTaskMutation.mutate(updatedTasks);\n  };\n\n  const handleEdit = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    if (onEdit) {\n      onEdit(card);\n    }\n  };\n\n  const handleCopy = async (e: React.MouseEvent) => {\n    e.stopPropagation();\n    try {\n      const cardText = `# ${card.title}\\n\\n${card.description}${card.link ? `\\n\\nLink: ${card.link}` : ''}`;\n      await navigator.clipboard.writeText(cardText);\n      setJustCopied(true);\n      toast({\n        title: \"Copied to clipboard\",\n        description: \"Card content has been copied as Markdown.\",\n      });\n      setTimeout(() => setJustCopied(false), 2000);\n    } catch (error) {\n      toast({\n        title: \"Copy failed\",\n        description: \"Unable to copy to clipboard. Please try again.\",\n        variant: \"destructive\",\n      });\n    }\n  };\n\n  // Hide the card completely after explosion\n  if (shouldHide) {\n    return null;\n  }\n\n  const style = {\n    transform: CSS.Transform.toString(transform),\n    transition,\n  };\n\n  const getBorderColor = () => {\n    const colors: Record<KanbanStatus, string> = {\n      \"not-started\": \"border-l-4 border-l-gray-400\",\n      \"blocked\": \"border-l-4 border-l-red-500\", \n      \"in-progress\": \"border-l-4 border-l-blue-500\",\n      \"complete\": \"border-l-4 border-l-green-500\",\n      \"verified\": \"border-l-4 border-l-purple-500\",\n    };\n    return colors[card.status as KanbanStatus] || \"border-l-4 border-l-gray-400\";\n  };\n\n  const getStatusIcon = () => {\n    switch (card.status) {\n      case \"blocked\":\n        return <AlertTriangle className=\"w-3 h-3 text-red-500\" />;\n      case \"in-progress\":\n        return <div className=\"w-2 h-2 rounded-full bg-blue-500 animate-pulse\" />;\n      case \"complete\":\n        return <CheckCircle className=\"w-3 h-3 text-green-500\" />;\n      case \"verified\":\n        return <Shield className=\"w-3 h-3 text-purple-500\" />;\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <ExplosionAnimation \n      trigger={isExploding} \n      onComplete={handleExplosionComplete}\n    >\n      <div\n        ref={setNodeRef}\n        style={style}\n        {...attributes}\n\n        className={cn(\n          \"task-card rounded-xl p-5 group relative flex flex-col min-w-[280px] bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm transition-colors duration-300\",\n          getBorderColor(),\n          isDragging && \"opacity-60 scale-105 rotate-2 shadow-2xl z-50\",\n          card._remoteUpdate && card._statusChanged && \"ring-2 ring-blue-400 ring-opacity-75 animate-pulse\",\n          isExpanded && \"min-h-fit\"\n        )}\n      >\n        <div className=\"flex items-start justify-between mb-2\">\n          <div className=\"flex-1 pr-2\">\n            <h4 className=\"font-medium text-gray-900 dark:text-gray-100 break-words mb-1\">{card.title}</h4>\n            {/* Project tag */}\n            {card.project && (\n              <div className=\"flex items-center\">\n                <span className=\"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300\">\n                  {card.project}\n                </span>\n              </div>\n            )}\n          </div>\n          \n          {/* Action buttons container */}\n          <div className=\"flex items-center space-x-1 ml-2 flex-shrink-0\">\n            {/* Copy button - only show on hover */}\n            <button\n              onClick={handleCopy}\n              className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/30 text-blue-500 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 z-10\"\n              title=\"Copy card content\"\n            >\n              {justCopied ? (\n                <Check className=\"w-3 h-3 text-green-500\" />\n              ) : (\n                <Copy className=\"w-3 h-3\" />\n              )}\n            </button>\n\n            {/* View button - only show on hover */}\n            <button\n              onClick={() => setShowViewDialog(true)}\n              className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 rounded-md hover:bg-purple-100 dark:hover:bg-purple-900/30 text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 z-10\"\n              title=\"View card details\"\n            >\n              <Eye className=\"w-3 h-3\" />\n            </button>\n\n            {/* Edit button - only show on hover */}\n            <button\n              onClick={handleEdit}\n              className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 rounded-md hover:bg-green-100 dark:hover:bg-green-900/30 text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 z-10\"\n              title=\"Edit card\"\n            >\n              <Edit3 className=\"w-3 h-3\" />\n            </button>\n            \n            {/* Delete button - only show on hover */}\n            <button\n              onClick={handleDelete}\n              className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 rounded-md hover:bg-red-100 dark:hover:bg-red-900/30 text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 z-10\"\n              title=\"Delete card\"\n            >\n              <Trash2 className=\"w-3 h-3\" />\n            </button>\n            \n            {/* Drag handle */}\n            <div className=\"p-1 cursor-grab active:cursor-grabbing\" {...listeners}>\n              <GripVertical className=\"w-4 h-4 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors\" />\n            </div>\n          </div>\n        </div>\n\n        {/* Task Progress Bar */}\n        {showProgress && (\n          <div className=\"mb-3\">\n            <div className=\"flex items-center justify-between mb-2\">\n              <span className=\"text-xs text-gray-600 dark:text-gray-300 font-medium\">\n                Progress: {taskProgress.completed}/{taskProgress.total} tasks\n              </span>\n              <span className=\"text-xs text-gray-500 dark:text-gray-400 font-bold\">\n                {taskProgress.percentage}%\n              </span>\n            </div>\n            <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n              <motion.div\n                className={cn(\n                  \"h-2 rounded-full transition-all duration-500\",\n                  taskProgress.percentage === 100 ? \"bg-green-500\" : \"bg-blue-500\"\n                )}\n                initial={{ width: 0 }}\n                animate={{ width: `${taskProgress.percentage}%` }}\n                transition={{ duration: 0.5, ease: \"easeOut\" }}\n              />\n            </div>\n          </div>\n        )}\n        \n        <div className=\"mb-3 flex-1\">\n          <motion.div\n            initial={false}\n            animate={{ \n              height: isExpanded ? \"auto\" : \"auto\"\n            }}\n            transition={{ duration: 0.2, ease: \"easeInOut\" }}\n          >\n            <InteractiveMarkdown\n              content={card.description}\n              tasks={tasks}\n              onTaskToggle={handleTaskToggle}\n              isExpanded={isExpanded}\n            />\n          </motion.div>\n          \n          {/* Show expand/collapse button only if content is long enough */}\n          {card.description && card.description.length > 100 && (\n            <motion.button\n              onClick={(e) => {\n                e.stopPropagation();\n                setIsExpanded(!isExpanded);\n              }}\n              className={cn(\n                \"mt-2 text-xs flex items-center space-x-1 font-medium transition-colors duration-150\",\n                card.status === \"in-progress\" \n                  ? \"text-blue-700 hover:text-blue-900\" \n                  : \"text-blue-600 hover:text-blue-800\"\n              )}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <span>{isExpanded ? \"Show less\" : \"Show more\"}</span>\n              <motion.div\n                animate={{ rotate: isExpanded ? 180 : 0 }}\n                transition={{ duration: 0.2 }}\n              >\n                <ChevronDown className=\"w-3 h-3\" />\n              </motion.div>\n            </motion.button>\n          )}\n\n\n        </div>\n        \n        <div className=\"flex items-center justify-between mt-auto\">\n          {card.link ? (\n            <a\n              href={card.link}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center space-x-1\"\n              onClick={(e) => e.stopPropagation()}\n            >\n              <span>View Details</span>\n              <ExternalLink className=\"w-3 h-3\" />\n            </a>\n          ) : (\n            <div></div>\n          )}\n          \n          <div className=\"flex items-center space-x-2\">\n            {getStatusIcon()}\n            <span className=\"text-xs text-gray-500\">ID: {card.id.slice(0, 8)}</span>\n          </div>\n        </div>\n      </div>\n      \n      {/* View Dialog */}\n      <ViewCardDialog\n        card={card}\n        open={showViewDialog}\n        onOpenChange={setShowViewDialog}\n        onEditCard={onEdit}\n      />\n    </ExplosionAnimation>\n  );\n}\n", "size_bytes": 13064}, "client/src/components/theme-provider.tsx": {"content": "import { createContext, useContext, useEffect, useState } from \"react\";\n\ntype Theme = \"light\" | \"dark\";\n\ntype ThemeProviderProps = {\n  children: React.ReactNode;\n  defaultTheme?: Theme;\n  storageKey?: string;\n};\n\ntype ThemeProviderState = {\n  theme: Theme;\n  setTheme: (theme: Theme) => void;\n  toggleTheme: () => void;\n};\n\nconst initialState: ThemeProviderState = {\n  theme: \"light\",\n  setTheme: () => null,\n  toggleTheme: () => null,\n};\n\nconst ThemeProviderContext = createContext<ThemeProviderState>(initialState);\n\nexport function ThemeProvider({\n  children,\n  defaultTheme = \"light\",\n  storageKey = \"kanban-theme\",\n  ...props\n}: ThemeProviderProps) {\n  const [theme, setTheme] = useState<Theme>(() => {\n    if (typeof window !== \"undefined\") {\n      return (localStorage.getItem(storageKey) as Theme) || defaultTheme;\n    }\n    return defaultTheme;\n  });\n\n  useEffect(() => {\n    const root = window.document.documentElement;\n    root.classList.remove(\"light\", \"dark\");\n    root.classList.add(theme);\n  }, [theme]);\n\n  const value = {\n    theme,\n    setTheme: (theme: Theme) => {\n      localStorage.setItem(storageKey, theme);\n      setTheme(theme);\n    },\n    toggleTheme: () => {\n      const newTheme = theme === \"light\" ? \"dark\" : \"light\";\n      localStorage.setItem(storageKey, newTheme);\n      setTheme(newTheme);\n    },\n  };\n\n  return (\n    <ThemeProviderContext.Provider {...props} value={value}>\n      {children}\n    </ThemeProviderContext.Provider>\n  );\n}\n\nexport const useTheme = () => {\n  const context = useContext(ThemeProviderContext);\n\n  if (context === undefined) {\n    throw new Error(\"useTheme must be used within a ThemeProvider\");\n  }\n\n  return context;\n};", "size_bytes": 1681}, "client/src/components/theme-toggle.tsx": {"content": "import { <PERSON>, Sun } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { useTheme } from \"./theme-provider\";\n\nexport function ThemeToggle() {\n  const { theme, toggleTheme } = useTheme();\n\n  return (\n    <Button\n      variant=\"outline\"\n      size=\"sm\"\n      onClick={toggleTheme}\n      className=\"relative bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 dark:from-gray-800 dark:to-gray-700 dark:hover:from-gray-700 dark:hover:to-gray-600 text-gray-700 dark:text-gray-200 border-gray-300 dark:border-gray-600 shadow-md hover:shadow-lg transition-all duration-300\"\n      title={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}\n    >\n      <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n      <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n      <span className=\"sr-only\">Toggle theme</span>\n    </Button>\n  );\n}", "size_bytes": 974}, "client/src/components/view-card-dialog.tsx": {"content": "import { Card } from \"@shared/schema\";\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from \"@/components/ui/dialog\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { ExternalLink, Edit, Copy, X } from \"lucide-react\";\nimport ReactMarkdown from \"react-markdown\";\nimport remarkGfm from \"remark-gfm\";\nimport { useState } from \"react\";\nimport { useToast } from \"@/hooks/use-toast\";\n\ninterface ViewCardDialogProps {\n  card: Card;\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  onEditCard?: (card: Card) => void;\n}\n\nexport function ViewCardDialog({ card, open, onOpenChange, onEditCard }: ViewCardDialogProps) {\n  const [isCopying, setIsCopying] = useState(false);\n  const { toast } = useToast();\n\n  const getStatusLabel = (status: string) => {\n    const labels: Record<string, string> = {\n      'not-started': 'Not Started',\n      'blocked': 'Blocked',\n      'in-progress': 'In Progress', \n      'complete': 'Complete',\n      'verified': 'Verified'\n    };\n    return labels[status] || status;\n  };\n\n  const getStatusColor = (status: string) => {\n    const colors: Record<string, string> = {\n      'not-started': 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',\n      'blocked': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',\n      'in-progress': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',\n      'complete': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',\n      'verified': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'\n    };\n    return colors[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200';\n  };\n\n  const handleCopy = async () => {\n    setIsCopying(true);\n    try {\n      const content = [\n        `# ${card.title}`,\n        '',\n        card.description || 'No description provided.',\n        '',\n        `**Status:** ${getStatusLabel(card.status)}`,\n        card.link ? `**Link:** ${card.link}` : '',\n        card.project ? `**Project:** ${card.project}` : ''\n      ].filter(Boolean).join('\\n');\n\n      await navigator.clipboard.writeText(content);\n      \n      toast({\n        title: \"Copied to clipboard\",\n        description: \"Card content has been copied as Markdown.\",\n      });\n    } catch (error) {\n      toast({\n        title: \"Copy failed\",\n        description: \"Could not copy to clipboard.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setTimeout(() => setIsCopying(false), 1000);\n    }\n  };\n\n  const handleEdit = () => {\n    onOpenChange(false);\n    onEditCard?.(card);\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"sm:max-w-[800px] max-h-[90vh] bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 shadow-2xl\">\n        <DialogHeader className=\"space-y-3\">\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex-1 min-w-0 pr-4\">\n              <DialogTitle className=\"text-2xl font-bold leading-tight text-gray-900 dark:text-gray-100\">\n                {card.title}\n              </DialogTitle>\n              <DialogDescription className=\"text-base text-gray-600 dark:text-gray-400 mt-2\">\n                Full card details and content\n              </DialogDescription>\n            </div>\n            <div className=\"flex items-center space-x-2 flex-shrink-0\">\n              <Badge className={getStatusColor(card.status)}>\n                {getStatusLabel(card.status)}\n              </Badge>\n            </div>\n          </div>\n          \n          {/* Action buttons */}\n          <div className=\"flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center space-x-2\">\n              {card.link && (\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => window.open(card.link!, '_blank')}\n                  className=\"bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700\"\n                >\n                  <ExternalLink className=\"w-4 h-4 mr-2\" />\n                  Open Link\n                </Button>\n              )}\n              {card.project && (\n                <Badge variant=\"outline\" className=\"bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900 dark:text-blue-200 dark:border-blue-700\">\n                  {card.project}\n                </Badge>\n              )}\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={handleCopy}\n                className=\"bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700 hover:bg-blue-100 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-200\"\n              >\n                {isCopying ? (\n                  <>\n                    <Copy className=\"w-4 h-4 mr-2 animate-pulse\" />\n                    Copied!\n                  </>\n                ) : (\n                  <>\n                    <Copy className=\"w-4 h-4 mr-2\" />\n                    Copy\n                  </>\n                )}\n              </Button>\n              {onEditCard && (\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={handleEdit}\n                  className=\"bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700 hover:bg-green-100 dark:hover:bg-green-800 text-green-700 dark:text-green-200\"\n                >\n                  <Edit className=\"w-4 h-4 mr-2\" />\n                  Edit\n                </Button>\n              )}\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => onOpenChange(false)}\n                className=\"bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700\"\n              >\n                <X className=\"w-4 h-4 mr-2\" />\n                Close\n              </Button>\n            </div>\n          </div>\n        </DialogHeader>\n        \n        <div className=\"overflow-y-auto flex-1 max-h-[60vh] px-1\">\n          {card.description ? (\n            <div className=\"text-sm text-gray-600 break-words prose prose-sm max-w-none\">\n              <ReactMarkdown \n                remarkPlugins={[remarkGfm]}\n                components={{\n                  // Use the same clean styling as InteractiveMarkdown\n                  p: ({ children }) => <p className=\"mb-2 last:mb-0 text-gray-700 dark:text-gray-300\">{children}</p>,\n                  ul: ({ children }) => <ul className=\"mb-2 last:mb-0 ml-2\">{children}</ul>,\n                  ol: ({ children }) => <ol className=\"mb-2 last:mb-0 ml-4\">{children}</ol>,\n                  li: ({ children }) => <li className=\"mb-1 ml-4 text-sm text-gray-700 dark:text-gray-300\">{children}</li>,\n                  code: ({ children }) => <code className=\"bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded text-sm text-gray-800 dark:text-gray-200\">{children}</code>,\n                  strong: ({ children }) => <strong className=\"font-semibold text-gray-800 dark:text-gray-200\">{children}</strong>,\n                  em: ({ children }) => <em className=\"italic text-gray-700 dark:text-gray-300\">{children}</em>,\n                  h1: ({ children }) => <h1 className=\"text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100\">{children}</h1>,\n                  h2: ({ children }) => <h2 className=\"text-base font-semibold mb-2 text-gray-900 dark:text-gray-100\">{children}</h2>,\n                  h3: ({ children }) => <h3 className=\"text-sm font-medium mb-1 text-gray-900 dark:text-gray-100\">{children}</h3>,\n                  blockquote: ({ children }) => <blockquote className=\"border-l-2 border-gray-300 dark:border-gray-600 pl-3 py-1 my-2 italic bg-gray-50 dark:bg-gray-800/30 text-gray-700 dark:text-gray-300\">{children}</blockquote>,\n                  a: ({ href, children }) => (\n                    <a \n                      href={href} \n                      target=\"_blank\" \n                      rel=\"noopener noreferrer\" \n                      className=\"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline\"\n                    >\n                      {children}\n                    </a>\n                  ),\n                  pre: ({ children }) => <pre className=\"bg-gray-100 dark:bg-gray-800 p-3 rounded-md overflow-x-auto my-2 text-sm\">{children}</pre>,\n                }}\n              >\n                {card.description}\n              </ReactMarkdown>\n            </div>\n          ) : (\n            <div className=\"text-gray-500 dark:text-gray-400 italic text-center py-8\">\n              No description provided\n            </div>\n          )}\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}", "size_bytes": 8943}, "client/src/hooks/use-mobile.tsx": {"content": "import * as React from \"react\"\n\nconst MO<PERSON>LE_BREAKPOINT = 768\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\n\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    }\n    mql.addEventListener(\"change\", onChange)\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    return () => mql.removeEventListener(\"change\", onChange)\n  }, [])\n\n  return !!isMobile\n}\n", "size_bytes": 565}, "client/src/hooks/use-toast.ts": {"content": "import * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"@/components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n", "size_bytes": 3895}, "client/src/hooks/use-websocket.ts": {"content": "import { useEffect, useRef, useState } from 'react';\nimport { useQueryClient } from '@tanstack/react-query';\nimport { Card } from '@shared/schema';\n\nexport function useWebSocket(options?: { onMessage?: (message: any) => void }) {\n  const queryClient = useQueryClient();\n  const wsRef = useRef<WebSocket | null>(null);\n  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  const [isConnected, setIsConnected] = useState(false);\n\n  const connect = () => {\n    try {\n      const protocol = window.location.protocol === \"https:\" ? \"wss:\" : \"ws:\";\n      const wsUrl = `${protocol}//${window.location.host}/ws`;\n      \n      console.log('Attempting to connect to WebSocket:', wsUrl);\n      console.log('Current location:', window.location.href);\n      \n      wsRef.current = new WebSocket(wsUrl);\n\n      wsRef.current.onopen = () => {\n        console.log('WebSocket connected successfully');\n        setIsConnected(true);\n      };\n\n      wsRef.current.onmessage = (event) => {\n        try {\n          const message = JSON.parse(event.data);\n          console.log('WebSocket message received:', message.type, message.data);\n          \n          // Call custom onMessage handler if provided\n          if (options?.onMessage) {\n            options.onMessage(message);\n          }\n          \n          switch (message.type) {\n            case 'INITIAL_DATA':\n              // Set initial cards data\n              queryClient.setQueryData(['/api/cards'], message.data);\n              break;\n              \n            case 'CARD_CREATED':\n              // Add new card to the cache and invalidate queries\n              queryClient.setQueryData(['/api/cards'], (oldData: Card[] = []) => {\n                return [...oldData, message.data];\n              });\n              queryClient.invalidateQueries({ queryKey: ['/api/cards'] });\n              queryClient.invalidateQueries({ queryKey: ['/api/projects'] });\n              break;\n              \n            case 'CARD_UPDATED':\n              // Update existing card in cache and invalidate queries\n              queryClient.setQueryData(['/api/cards'], (oldData: Card[] = []) => {\n                const oldCard = oldData.find(card => card.id === message.data.id);\n                const statusChanged = oldCard && oldCard.status !== message.data.status;\n                \n                return oldData.map(card => \n                  card.id === message.data.id ? {\n                    ...message.data,\n                    _remoteUpdate: true,\n                    _statusChanged: statusChanged\n                  } : card\n                );\n              });\n              queryClient.invalidateQueries({ queryKey: ['/api/cards'] });\n              \n              // Clear the remote update flag after animation\n              setTimeout(() => {\n                queryClient.setQueryData(['/api/cards'], (currentData: Card[] = []) => {\n                  return currentData.map(card => {\n                    const { _remoteUpdate, _statusChanged, ...cleanCard } = card as Card & { \n                      _remoteUpdate?: boolean; \n                      _statusChanged?: boolean; \n                    };\n                    return cleanCard;\n                  });\n                });\n              }, 600);\n              break;\n              \n            case 'CARD_DELETED':\n              // Remove card from cache and invalidate queries\n              queryClient.setQueryData(['/api/cards'], (oldData: Card[] = []) => {\n                return oldData.filter(card => card.id !== message.data.id);\n              });\n              queryClient.invalidateQueries({ queryKey: ['/api/cards'] });\n              queryClient.invalidateQueries({ queryKey: ['/api/projects'] });\n              break;\n              \n            case 'CARDS_BULK_DELETED':\n              // Remove multiple cards from cache and invalidate queries\n              queryClient.setQueryData(['/api/cards'], (oldData: Card[] = []) => {\n                const deletedIds = message.data;\n                return oldData.filter(card => !deletedIds.includes(card.id));\n              });\n              queryClient.invalidateQueries({ queryKey: ['/api/cards'] });\n              queryClient.invalidateQueries({ queryKey: ['/api/projects'] });\n              break;\n              \n            default:\n              console.log('Unknown WebSocket message type:', message.type);\n          }\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n\n      wsRef.current.onclose = (event) => {\n        console.log('WebSocket disconnected', event.code, event.reason);\n        setIsConnected(false);\n        // Attempt to reconnect after 3 seconds\n        reconnectTimeoutRef.current = setTimeout(connect, 3000);\n      };\n\n      wsRef.current.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        setIsConnected(false);\n      };\n\n    } catch (error) {\n      console.error('Failed to connect WebSocket:', error);\n      // Retry connection after 5 seconds\n      reconnectTimeoutRef.current = setTimeout(connect, 5000);\n    }\n  };\n\n  useEffect(() => {\n    connect();\n\n    return () => {\n      if (reconnectTimeoutRef.current) {\n        clearTimeout(reconnectTimeoutRef.current);\n      }\n      if (wsRef.current) {\n        wsRef.current.close();\n      }\n    };\n  }, []);\n\n  return {\n    isConnected,\n    ws: wsRef.current\n  };\n}", "size_bytes": 5401}, "client/src/lib/queryClient.ts": {"content": "import { QueryClient, QueryFunction } from \"@tanstack/react-query\";\n\nasync function throwIfResNotOk(res: Response) {\n  if (!res.ok) {\n    const text = (await res.text()) || res.statusText;\n    throw new Error(`${res.status}: ${text}`);\n  }\n}\n\nexport async function apiRequest(\n  method: string,\n  url: string,\n  data?: unknown | undefined,\n): Promise<Response> {\n  const res = await fetch(url, {\n    method,\n    headers: data ? { \"Content-Type\": \"application/json\" } : {},\n    body: data ? JSON.stringify(data) : undefined,\n    credentials: \"include\",\n  });\n\n  await throwIfResNotOk(res);\n  return res;\n}\n\ntype UnauthorizedBehavior = \"returnNull\" | \"throw\";\nexport const getQueryFn: <T>(options: {\n  on401: UnauthorizedBehavior;\n}) => QueryFunction<T> =\n  ({ on401: unauthorizedBehavior }) =>\n  async ({ queryKey }) => {\n    const res = await fetch(queryKey.join(\"/\") as string, {\n      credentials: \"include\",\n    });\n\n    if (unauthorizedBehavior === \"returnNull\" && res.status === 401) {\n      return null;\n    }\n\n    await throwIfResNotOk(res);\n    return await res.json();\n  };\n\nexport const queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      queryFn: getQueryFn({ on401: \"throw\" }),\n      refetchInterval: false,\n      refetchOnWindowFocus: false,\n      staleTime: Infinity,\n      retry: false,\n    },\n    mutations: {\n      retry: false,\n    },\n  },\n});\n", "size_bytes": 1383}, "client/src/lib/task-utils.ts": {"content": "export interface TaskItem {\n  id: string;\n  text: string;\n  completed: boolean;\n}\n\nexport function parseTaskList(taskListJson?: string | null): TaskItem[] {\n  if (!taskListJson) return [];\n  \n  try {\n    const parsed = JSON.parse(taskListJson);\n    return Array.isArray(parsed) ? parsed : [];\n  } catch {\n    return [];\n  }\n}\n\nexport function serializeTaskList(tasks: TaskItem[]): string {\n  return JSON.stringify(tasks);\n}\n\nexport function extractTasksFromMarkdown(markdown: string): TaskItem[] {\n  // Extract GitHub-style task lists from markdown\n  const taskRegex = /^[-*+]\\s*\\[([x\\s])\\]\\s*(.+)$/gm;\n  const tasks: TaskItem[] = [];\n  let match;\n  \n  while ((match = taskRegex.exec(markdown)) !== null) {\n    const text = match[2].trim();\n    const isCompleted = match[1].toLowerCase() === 'x';\n    \n    tasks.push({\n      id: text.toLowerCase().replace(/[^a-z0-9]/g, ''), // Generate stable ID from text\n      text: text,\n      completed: isCompleted\n    });\n  }\n  \n\n  return tasks;\n}\n\nexport function calculateTaskProgress(tasks: TaskItem[]): {\n  completed: number;\n  total: number;\n  percentage: number;\n} {\n  const total = tasks.length;\n  const completed = tasks.filter(task => task.completed).length;\n  const percentage = total === 0 ? 0 : Math.round((completed / total) * 100);\n  \n  return { completed, total, percentage };\n}\n\nexport function updateTaskCompletion(tasks: TaskItem[], taskId: string, completed: boolean): TaskItem[] {\n  return tasks.map(task => \n    task.id === taskId ? { ...task, completed } : task\n  );\n}\n\nexport function hasTaskList(description: string): boolean {\n  return /^[-*+]\\s*\\[([x\\s])\\]/m.test(description);\n}", "size_bytes": 1645}, "client/src/lib/utils.ts": {"content": "import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n", "size_bytes": 166}, "client/src/pages/kanban.tsx": {"content": "import { useState } from \"react\";\nimport { useQuery } from \"@tanstack/react-query\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\nimport { RotateCcw } from \"lucide-react\";\nimport { CreateProjectDialog } from \"@/components/create-project-dialog\";\nimport { KanbanBoard } from \"@/components/kanban-board\";\nimport { ThemeToggle } from \"@/components/theme-toggle\";\nimport { CardsSummary } from \"@/components/cards-summary\";\nimport { useToast } from \"@/hooks/use-toast\";\n\nexport default function Kanban() {\n  const [selectedProject, setSelectedProject] = useState<string>(\"all\");\n  const { toast } = useToast();\n\n  const { data: projects = [] } = useQuery({\n    queryKey: ['/api/projects'],\n    queryFn: () => fetch('/api/projects').then(res => res.json())\n  });\n\n  const resetColumnWidths = () => {\n    localStorage.removeItem('kanban-column-widths');\n    window.location.reload();\n    toast({\n      title: \"Column widths reset\",\n      description: \"All columns have been restored to default width (320px).\",\n    });\n  };\n\n  const formatProjectName = (project: string) => {\n    return project\n      .split(/[-_\\s]+/)\n      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())\n      .join(' ');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300\">\n      <header className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4 transition-colors duration-300\">\n        <div className=\"flex items-center justify-between max-w-7xl mx-auto\">\n          <div className=\"flex items-center space-x-4\">\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">Kanban Board</h1>\n            <span className=\"text-sm text-gray-500 dark:text-gray-400\">Multi-Project Management</span>\n            \n            <div className=\"flex items-center space-x-2\">\n              <Select value={selectedProject} onValueChange={setSelectedProject}>\n                <SelectTrigger className=\"w-48\">\n                  <SelectValue placeholder=\"All Projects\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">All Projects</SelectItem>\n                  {projects.map((project: string) => (\n                    <SelectItem key={project} value={project}>\n                      {formatProjectName(project)}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n              \n              <CreateProjectDialog \n                open={false} \n                onOpenChange={() => {}} \n                onProjectCreated={() => {}} \n              />\n              \n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={resetColumnWidths}\n                className=\"flex items-center gap-2\"\n                title=\"Reset column widths to default\"\n              >\n                <RotateCcw className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n          <ThemeToggle />\n        </div>\n      </header>\n      <main className=\"w-full py-8\">\n        <KanbanBoard selectedProject={selectedProject === \"all\" ? undefined : selectedProject} />\n        <CardsSummary selectedProject={selectedProject === \"all\" ? undefined : selectedProject} />\n      </main>\n    </div>\n  );\n}\n", "size_bytes": 3461}, "client/src/pages/not-found.tsx": {"content": "import { Card, CardContent } from \"@/components/ui/card\";\nimport { AlertCircle } from \"lucide-react\";\n\nexport default function NotFound() {\n  return (\n    <div className=\"min-h-screen w-full flex items-center justify-center bg-gray-50\">\n      <Card className=\"w-full max-w-md mx-4\">\n        <CardContent className=\"pt-6\">\n          <div className=\"flex mb-4 gap-2\">\n            <AlertCircle className=\"h-8 w-8 text-red-500\" />\n            <h1 className=\"text-2xl font-bold text-gray-900\">404 Page Not Found</h1>\n          </div>\n\n          <p className=\"mt-4 text-sm text-gray-600\">\n            Did you forget to add the page to the router?\n          </p>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n", "size_bytes": 711}, "client/src/components/ui/accordion.tsx": {"content": "import * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDown } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Accordion = AccordionPrimitive.Root\n\nconst AccordionItem = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>\n>(({ className, ...props }, ref) => (\n  <AccordionPrimitive.Item\n    ref={ref}\n    className={cn(\"border-b\", className)}\n    {...props}\n  />\n))\nAccordionItem.displayName = \"AccordionItem\"\n\nconst AccordionTrigger = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Header className=\"flex\">\n    <AccordionPrimitive.Trigger\n      ref={ref}\n      className={cn(\n        \"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronDown className=\"h-4 w-4 shrink-0 transition-transform duration-200\" />\n    </AccordionPrimitive.Trigger>\n  </AccordionPrimitive.Header>\n))\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName\n\nconst AccordionContent = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Content\n    ref={ref}\n    className=\"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\n    {...props}\n  >\n    <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\n  </AccordionPrimitive.Content>\n))\n\nAccordionContent.displayName = AccordionPrimitive.Content.displayName\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n", "size_bytes": 1977}, "client/src/components/ui/alert-dialog.tsx": {"content": "import * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n", "size_bytes": 4420}, "client/src/components/ui/alert.tsx": {"content": "import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n", "size_bytes": 1584}, "client/src/components/ui/aspect-ratio.tsx": {"content": "import * as AspectRatioPrimitive from \"@radix-ui/react-aspect-ratio\"\n\nconst AspectRatio = AspectRatioPrimitive.Root\n\nexport { AspectRatio }\n", "size_bytes": 140}, "client/src/components/ui/avatar.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n", "size_bytes": 1419}, "client/src/components/ui/badge.tsx": {"content": "import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n", "size_bytes": 1128}, "client/src/components/ui/breadcrumb.tsx": {"content": "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Breadcrumb = React.forwardRef<\n  HTMLElement,\n  React.ComponentPropsWithoutRef<\"nav\"> & {\n    separator?: React.ReactNode\n  }\n>(({ ...props }, ref) => <nav ref={ref} aria-label=\"breadcrumb\" {...props} />)\nBreadcrumb.displayName = \"Breadcrumb\"\n\nconst BreadcrumbList = React.forwardRef<\n  HTMLOListElement,\n  React.ComponentPropsWithoutRef<\"ol\">\n>(({ className, ...props }, ref) => (\n  <ol\n    ref={ref}\n    className={cn(\n      \"flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5\",\n      className\n    )}\n    {...props}\n  />\n))\nBreadcrumbList.displayName = \"BreadcrumbList\"\n\nconst BreadcrumbItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentPropsWithoutRef<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    className={cn(\"inline-flex items-center gap-1.5\", className)}\n    {...props}\n  />\n))\nBreadcrumbItem.displayName = \"BreadcrumbItem\"\n\nconst BreadcrumbLink = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentPropsWithoutRef<\"a\"> & {\n    asChild?: boolean\n  }\n>(({ asChild, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      ref={ref}\n      className={cn(\"transition-colors hover:text-foreground\", className)}\n      {...props}\n    />\n  )\n})\nBreadcrumbLink.displayName = \"BreadcrumbLink\"\n\nconst BreadcrumbPage = React.forwardRef<\n  HTMLSpanElement,\n  React.ComponentPropsWithoutRef<\"span\">\n>(({ className, ...props }, ref) => (\n  <span\n    ref={ref}\n    role=\"link\"\n    aria-disabled=\"true\"\n    aria-current=\"page\"\n    className={cn(\"font-normal text-foreground\", className)}\n    {...props}\n  />\n))\nBreadcrumbPage.displayName = \"BreadcrumbPage\"\n\nconst BreadcrumbSeparator = ({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) => (\n  <li\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"[&>svg]:w-3.5 [&>svg]:h-3.5\", className)}\n    {...props}\n  >\n    {children ?? <ChevronRight />}\n  </li>\n)\nBreadcrumbSeparator.displayName = \"BreadcrumbSeparator\"\n\nconst BreadcrumbEllipsis = ({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) => (\n  <span\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\n    {...props}\n  >\n    <MoreHorizontal className=\"h-4 w-4\" />\n    <span className=\"sr-only\">More</span>\n  </span>\n)\nBreadcrumbEllipsis.displayName = \"BreadcrumbElipssis\"\n\nexport {\n  Breadcrumb,\n  BreadcrumbList,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n  BreadcrumbEllipsis,\n}\n", "size_bytes": 2712}, "client/src/components/ui/button.tsx": {"content": "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n", "size_bytes": 1901}, "client/src/components/ui/calendar.tsx": {"content": "import * as React from \"react\"\nimport { ChevronLeft, ChevronRight } from \"lucide-react\"\nimport { DayPicker } from \"react-day-picker\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nexport type CalendarProps = React.ComponentProps<typeof DayPicker>\n\nfunction Calendar({\n  className,\n  classNames,\n  showOutsideDays = true,\n  ...props\n}: CalendarProps) {\n  return (\n    <DayPicker\n      showOutsideDays={showOutsideDays}\n      className={cn(\"p-3\", className)}\n      classNames={{\n        months: \"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0\",\n        month: \"space-y-4\",\n        caption: \"flex justify-center pt-1 relative items-center\",\n        caption_label: \"text-sm font-medium\",\n        nav: \"space-x-1 flex items-center\",\n        nav_button: cn(\n          buttonVariants({ variant: \"outline\" }),\n          \"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\n        ),\n        nav_button_previous: \"absolute left-1\",\n        nav_button_next: \"absolute right-1\",\n        table: \"w-full border-collapse space-y-1\",\n        head_row: \"flex\",\n        head_cell:\n          \"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]\",\n        row: \"flex w-full mt-2\",\n        cell: \"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20\",\n        day: cn(\n          buttonVariants({ variant: \"ghost\" }),\n          \"h-9 w-9 p-0 font-normal aria-selected:opacity-100\"\n        ),\n        day_range_end: \"day-range-end\",\n        day_selected:\n          \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\n        day_today: \"bg-accent text-accent-foreground\",\n        day_outside:\n          \"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground\",\n        day_disabled: \"text-muted-foreground opacity-50\",\n        day_range_middle:\n          \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\n        day_hidden: \"invisible\",\n        ...classNames,\n      }}\n      components={{\n        IconLeft: ({ className, ...props }) => (\n          <ChevronLeft className={cn(\"h-4 w-4\", className)} {...props} />\n        ),\n        IconRight: ({ className, ...props }) => (\n          <ChevronRight className={cn(\"h-4 w-4\", className)} {...props} />\n        ),\n      }}\n      {...props}\n    />\n  )\n}\nCalendar.displayName = \"Calendar\"\n\nexport { Calendar }\n", "size_bytes": 2695}, "client/src/components/ui/card.tsx": {"content": "import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n", "size_bytes": 1858}, "client/src/components/ui/carousel.tsx": {"content": "import * as React from \"react\"\nimport useEmblaCarousel, {\n  type UseEmblaCarouselType,\n} from \"embla-carousel-react\"\nimport { ArrowLeft, ArrowRight } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\n\ntype CarouselApi = UseEmblaCarouselType[1]\ntype UseCarouselParameters = Parameters<typeof useEmblaCarousel>\ntype CarouselOptions = UseCarouselParameters[0]\ntype CarouselPlugin = UseCarouselParameters[1]\n\ntype CarouselProps = {\n  opts?: CarouselOptions\n  plugins?: CarouselPlugin\n  orientation?: \"horizontal\" | \"vertical\"\n  setApi?: (api: CarouselApi) => void\n}\n\ntype CarouselContextProps = {\n  carouselRef: ReturnType<typeof useEmblaCarousel>[0]\n  api: ReturnType<typeof useEmblaCarousel>[1]\n  scrollPrev: () => void\n  scrollNext: () => void\n  canScrollPrev: boolean\n  canScrollNext: boolean\n} & CarouselProps\n\nconst CarouselContext = React.createContext<CarouselContextProps | null>(null)\n\nfunction useCarousel() {\n  const context = React.useContext(CarouselContext)\n\n  if (!context) {\n    throw new Error(\"useCarousel must be used within a <Carousel />\")\n  }\n\n  return context\n}\n\nconst Carousel = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & CarouselProps\n>(\n  (\n    {\n      orientation = \"horizontal\",\n      opts,\n      setApi,\n      plugins,\n      className,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const [carouselRef, api] = useEmblaCarousel(\n      {\n        ...opts,\n        axis: orientation === \"horizontal\" ? \"x\" : \"y\",\n      },\n      plugins\n    )\n    const [canScrollPrev, setCanScrollPrev] = React.useState(false)\n    const [canScrollNext, setCanScrollNext] = React.useState(false)\n\n    const onSelect = React.useCallback((api: CarouselApi) => {\n      if (!api) {\n        return\n      }\n\n      setCanScrollPrev(api.canScrollPrev())\n      setCanScrollNext(api.canScrollNext())\n    }, [])\n\n    const scrollPrev = React.useCallback(() => {\n      api?.scrollPrev()\n    }, [api])\n\n    const scrollNext = React.useCallback(() => {\n      api?.scrollNext()\n    }, [api])\n\n    const handleKeyDown = React.useCallback(\n      (event: React.KeyboardEvent<HTMLDivElement>) => {\n        if (event.key === \"ArrowLeft\") {\n          event.preventDefault()\n          scrollPrev()\n        } else if (event.key === \"ArrowRight\") {\n          event.preventDefault()\n          scrollNext()\n        }\n      },\n      [scrollPrev, scrollNext]\n    )\n\n    React.useEffect(() => {\n      if (!api || !setApi) {\n        return\n      }\n\n      setApi(api)\n    }, [api, setApi])\n\n    React.useEffect(() => {\n      if (!api) {\n        return\n      }\n\n      onSelect(api)\n      api.on(\"reInit\", onSelect)\n      api.on(\"select\", onSelect)\n\n      return () => {\n        api?.off(\"select\", onSelect)\n      }\n    }, [api, onSelect])\n\n    return (\n      <CarouselContext.Provider\n        value={{\n          carouselRef,\n          api: api,\n          opts,\n          orientation:\n            orientation || (opts?.axis === \"y\" ? \"vertical\" : \"horizontal\"),\n          scrollPrev,\n          scrollNext,\n          canScrollPrev,\n          canScrollNext,\n        }}\n      >\n        <div\n          ref={ref}\n          onKeyDownCapture={handleKeyDown}\n          className={cn(\"relative\", className)}\n          role=\"region\"\n          aria-roledescription=\"carousel\"\n          {...props}\n        >\n          {children}\n        </div>\n      </CarouselContext.Provider>\n    )\n  }\n)\nCarousel.displayName = \"Carousel\"\n\nconst CarouselContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => {\n  const { carouselRef, orientation } = useCarousel()\n\n  return (\n    <div ref={carouselRef} className=\"overflow-hidden\">\n      <div\n        ref={ref}\n        className={cn(\n          \"flex\",\n          orientation === \"horizontal\" ? \"-ml-4\" : \"-mt-4 flex-col\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n})\nCarouselContent.displayName = \"CarouselContent\"\n\nconst CarouselItem = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => {\n  const { orientation } = useCarousel()\n\n  return (\n    <div\n      ref={ref}\n      role=\"group\"\n      aria-roledescription=\"slide\"\n      className={cn(\n        \"min-w-0 shrink-0 grow-0 basis-full\",\n        orientation === \"horizontal\" ? \"pl-4\" : \"pt-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nCarouselItem.displayName = \"CarouselItem\"\n\nconst CarouselPrevious = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<typeof Button>\n>(({ className, variant = \"outline\", size = \"icon\", ...props }, ref) => {\n  const { orientation, scrollPrev, canScrollPrev } = useCarousel()\n\n  return (\n    <Button\n      ref={ref}\n      variant={variant}\n      size={size}\n      className={cn(\n        \"absolute  h-8 w-8 rounded-full\",\n        orientation === \"horizontal\"\n          ? \"-left-12 top-1/2 -translate-y-1/2\"\n          : \"-top-12 left-1/2 -translate-x-1/2 rotate-90\",\n        className\n      )}\n      disabled={!canScrollPrev}\n      onClick={scrollPrev}\n      {...props}\n    >\n      <ArrowLeft className=\"h-4 w-4\" />\n      <span className=\"sr-only\">Previous slide</span>\n    </Button>\n  )\n})\nCarouselPrevious.displayName = \"CarouselPrevious\"\n\nconst CarouselNext = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<typeof Button>\n>(({ className, variant = \"outline\", size = \"icon\", ...props }, ref) => {\n  const { orientation, scrollNext, canScrollNext } = useCarousel()\n\n  return (\n    <Button\n      ref={ref}\n      variant={variant}\n      size={size}\n      className={cn(\n        \"absolute h-8 w-8 rounded-full\",\n        orientation === \"horizontal\"\n          ? \"-right-12 top-1/2 -translate-y-1/2\"\n          : \"-bottom-12 left-1/2 -translate-x-1/2 rotate-90\",\n        className\n      )}\n      disabled={!canScrollNext}\n      onClick={scrollNext}\n      {...props}\n    >\n      <ArrowRight className=\"h-4 w-4\" />\n      <span className=\"sr-only\">Next slide</span>\n    </Button>\n  )\n})\nCarouselNext.displayName = \"CarouselNext\"\n\nexport {\n  type CarouselApi,\n  Carousel,\n  CarouselContent,\n  CarouselItem,\n  CarouselPrevious,\n  CarouselNext,\n}\n", "size_bytes": 6210}, "client/src/components/ui/chart.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as RechartsPrimitive from \"recharts\"\n\nimport { cn } from \"@/lib/utils\"\n\n// Format: { THEME_NAME: CSS_SELECTOR }\nconst THEMES = { light: \"\", dark: \".dark\" } as const\n\nexport type ChartConfig = {\n  [k in string]: {\n    label?: React.ReactNode\n    icon?: React.ComponentType\n  } & (\n    | { color?: string; theme?: never }\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\n  )\n}\n\ntype ChartContextProps = {\n  config: ChartConfig\n}\n\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\n\nfunction useChart() {\n  const context = React.useContext(ChartContext)\n\n  if (!context) {\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\n  }\n\n  return context\n}\n\nconst ChartContainer = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    config: ChartConfig\n    children: React.ComponentProps<\n      typeof RechartsPrimitive.ResponsiveContainer\n    >[\"children\"]\n  }\n>(({ id, className, children, config, ...props }, ref) => {\n  const uniqueId = React.useId()\n  const chartId = `chart-${id || uniqueId.replace(/:/g, \"\")}`\n\n  return (\n    <ChartContext.Provider value={{ config }}>\n      <div\n        data-chart={chartId}\n        ref={ref}\n        className={cn(\n          \"flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none\",\n          className\n        )}\n        {...props}\n      >\n        <ChartStyle id={chartId} config={config} />\n        <RechartsPrimitive.ResponsiveContainer>\n          {children}\n        </RechartsPrimitive.ResponsiveContainer>\n      </div>\n    </ChartContext.Provider>\n  )\n})\nChartContainer.displayName = \"Chart\"\n\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\n  const colorConfig = Object.entries(config).filter(\n    ([, config]) => config.theme || config.color\n  )\n\n  if (!colorConfig.length) {\n    return null\n  }\n\n  return (\n    <style\n      dangerouslySetInnerHTML={{\n        __html: Object.entries(THEMES)\n          .map(\n            ([theme, prefix]) => `\n${prefix} [data-chart=${id}] {\n${colorConfig\n  .map(([key, itemConfig]) => {\n    const color =\n      itemConfig.theme?.[theme as keyof typeof itemConfig.theme] ||\n      itemConfig.color\n    return color ? `  --color-${key}: ${color};` : null\n  })\n  .join(\"\\n\")}\n}\n`\n          )\n          .join(\"\\n\"),\n      }}\n    />\n  )\n}\n\nconst ChartTooltip = RechartsPrimitive.Tooltip\n\nconst ChartTooltipContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\n    React.ComponentProps<\"div\"> & {\n      hideLabel?: boolean\n      hideIndicator?: boolean\n      indicator?: \"line\" | \"dot\" | \"dashed\"\n      nameKey?: string\n      labelKey?: string\n    }\n>(\n  (\n    {\n      active,\n      payload,\n      className,\n      indicator = \"dot\",\n      hideLabel = false,\n      hideIndicator = false,\n      label,\n      labelFormatter,\n      labelClassName,\n      formatter,\n      color,\n      nameKey,\n      labelKey,\n    },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    const tooltipLabel = React.useMemo(() => {\n      if (hideLabel || !payload?.length) {\n        return null\n      }\n\n      const [item] = payload\n      const key = `${labelKey || item?.dataKey || item?.name || \"value\"}`\n      const itemConfig = getPayloadConfigFromPayload(config, item, key)\n      const value =\n        !labelKey && typeof label === \"string\"\n          ? config[label as keyof typeof config]?.label || label\n          : itemConfig?.label\n\n      if (labelFormatter) {\n        return (\n          <div className={cn(\"font-medium\", labelClassName)}>\n            {labelFormatter(value, payload)}\n          </div>\n        )\n      }\n\n      if (!value) {\n        return null\n      }\n\n      return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>\n    }, [\n      label,\n      labelFormatter,\n      payload,\n      hideLabel,\n      labelClassName,\n      config,\n      labelKey,\n    ])\n\n    if (!active || !payload?.length) {\n      return null\n    }\n\n    const nestLabel = payload.length === 1 && indicator !== \"dot\"\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl\",\n          className\n        )}\n      >\n        {!nestLabel ? tooltipLabel : null}\n        <div className=\"grid gap-1.5\">\n          {payload.map((item, index) => {\n            const key = `${nameKey || item.name || item.dataKey || \"value\"}`\n            const itemConfig = getPayloadConfigFromPayload(config, item, key)\n            const indicatorColor = color || item.payload.fill || item.color\n\n            return (\n              <div\n                key={item.dataKey}\n                className={cn(\n                  \"flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground\",\n                  indicator === \"dot\" && \"items-center\"\n                )}\n              >\n                {formatter && item?.value !== undefined && item.name ? (\n                  formatter(item.value, item.name, item, index, item.payload)\n                ) : (\n                  <>\n                    {itemConfig?.icon ? (\n                      <itemConfig.icon />\n                    ) : (\n                      !hideIndicator && (\n                        <div\n                          className={cn(\n                            \"shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]\",\n                            {\n                              \"h-2.5 w-2.5\": indicator === \"dot\",\n                              \"w-1\": indicator === \"line\",\n                              \"w-0 border-[1.5px] border-dashed bg-transparent\":\n                                indicator === \"dashed\",\n                              \"my-0.5\": nestLabel && indicator === \"dashed\",\n                            }\n                          )}\n                          style={\n                            {\n                              \"--color-bg\": indicatorColor,\n                              \"--color-border\": indicatorColor,\n                            } as React.CSSProperties\n                          }\n                        />\n                      )\n                    )}\n                    <div\n                      className={cn(\n                        \"flex flex-1 justify-between leading-none\",\n                        nestLabel ? \"items-end\" : \"items-center\"\n                      )}\n                    >\n                      <div className=\"grid gap-1.5\">\n                        {nestLabel ? tooltipLabel : null}\n                        <span className=\"text-muted-foreground\">\n                          {itemConfig?.label || item.name}\n                        </span>\n                      </div>\n                      {item.value && (\n                        <span className=\"font-mono font-medium tabular-nums text-foreground\">\n                          {item.value.toLocaleString()}\n                        </span>\n                      )}\n                    </div>\n                  </>\n                )}\n              </div>\n            )\n          })}\n        </div>\n      </div>\n    )\n  }\n)\nChartTooltipContent.displayName = \"ChartTooltip\"\n\nconst ChartLegend = RechartsPrimitive.Legend\n\nconst ChartLegendContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> &\n    Pick<RechartsPrimitive.LegendProps, \"payload\" | \"verticalAlign\"> & {\n      hideIcon?: boolean\n      nameKey?: string\n    }\n>(\n  (\n    { className, hideIcon = false, payload, verticalAlign = \"bottom\", nameKey },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    if (!payload?.length) {\n      return null\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"flex items-center justify-center gap-4\",\n          verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\n          className\n        )}\n      >\n        {payload.map((item) => {\n          const key = `${nameKey || item.dataKey || \"value\"}`\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\n\n          return (\n            <div\n              key={item.value}\n              className={cn(\n                \"flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground\"\n              )}\n            >\n              {itemConfig?.icon && !hideIcon ? (\n                <itemConfig.icon />\n              ) : (\n                <div\n                  className=\"h-2 w-2 shrink-0 rounded-[2px]\"\n                  style={{\n                    backgroundColor: item.color,\n                  }}\n                />\n              )}\n              {itemConfig?.label}\n            </div>\n          )\n        })}\n      </div>\n    )\n  }\n)\nChartLegendContent.displayName = \"ChartLegend\"\n\n// Helper to extract item config from a payload.\nfunction getPayloadConfigFromPayload(\n  config: ChartConfig,\n  payload: unknown,\n  key: string\n) {\n  if (typeof payload !== \"object\" || payload === null) {\n    return undefined\n  }\n\n  const payloadPayload =\n    \"payload\" in payload &&\n    typeof payload.payload === \"object\" &&\n    payload.payload !== null\n      ? payload.payload\n      : undefined\n\n  let configLabelKey: string = key\n\n  if (\n    key in payload &&\n    typeof payload[key as keyof typeof payload] === \"string\"\n  ) {\n    configLabelKey = payload[key as keyof typeof payload] as string\n  } else if (\n    payloadPayload &&\n    key in payloadPayload &&\n    typeof payloadPayload[key as keyof typeof payloadPayload] === \"string\"\n  ) {\n    configLabelKey = payloadPayload[\n      key as keyof typeof payloadPayload\n    ] as string\n  }\n\n  return configLabelKey in config\n    ? config[configLabelKey]\n    : config[key as keyof typeof config]\n}\n\nexport {\n  ChartContainer,\n  ChartTooltip,\n  ChartTooltipContent,\n  ChartLegend,\n  ChartLegendContent,\n  ChartStyle,\n}\n", "size_bytes": 10481}, "client/src/components/ui/checkbox.tsx": {"content": "import * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n", "size_bytes": 1056}, "client/src/components/ui/collapsible.tsx": {"content": "\"use client\"\n\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\n\nconst Collapsible = CollapsiblePrimitive.Root\n\nconst CollapsibleTrigger = CollapsiblePrimitive.CollapsibleTrigger\n\nconst CollapsibleContent = CollapsiblePrimitive.CollapsibleContent\n\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\n", "size_bytes": 329}, "client/src/components/ui/command.tsx": {"content": "import * as React from \"react\"\nimport { type DialogProps } from \"@radix-ui/react-dialog\"\nimport { Command as CommandPrimitive } from \"cmdk\"\nimport { Search } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Dialog, DialogContent } from \"@/components/ui/dialog\"\n\nconst Command = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nCommand.displayName = CommandPrimitive.displayName\n\nconst CommandDialog = ({ children, ...props }: DialogProps) => {\n  return (\n    <Dialog {...props}>\n      <DialogContent className=\"overflow-hidden p-0 shadow-lg\">\n        <Command className=\"[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\">\n          {children}\n        </Command>\n      </DialogContent>\n    </Dialog>\n  )\n}\n\nconst CommandInput = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Input>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Input>\n>(({ className, ...props }, ref) => (\n  <div className=\"flex items-center border-b px-3\" cmdk-input-wrapper=\"\">\n    <Search className=\"mr-2 h-4 w-4 shrink-0 opacity-50\" />\n    <CommandPrimitive.Input\n      ref={ref}\n      className={cn(\n        \"flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  </div>\n))\n\nCommandInput.displayName = CommandPrimitive.Input.displayName\n\nconst CommandList = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.List\n    ref={ref}\n    className={cn(\"max-h-[300px] overflow-y-auto overflow-x-hidden\", className)}\n    {...props}\n  />\n))\n\nCommandList.displayName = CommandPrimitive.List.displayName\n\nconst CommandEmpty = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Empty>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Empty>\n>((props, ref) => (\n  <CommandPrimitive.Empty\n    ref={ref}\n    className=\"py-6 text-center text-sm\"\n    {...props}\n  />\n))\n\nCommandEmpty.displayName = CommandPrimitive.Empty.displayName\n\nconst CommandGroup = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Group>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Group>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.Group\n    ref={ref}\n    className={cn(\n      \"overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\n\nCommandGroup.displayName = CommandPrimitive.Group.displayName\n\nconst CommandSeparator = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 h-px bg-border\", className)}\n    {...props}\n  />\n))\nCommandSeparator.displayName = CommandPrimitive.Separator.displayName\n\nconst CommandItem = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Item>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      className\n    )}\n    {...props}\n  />\n))\n\nCommandItem.displayName = CommandPrimitive.Item.displayName\n\nconst CommandShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\n        \"ml-auto text-xs tracking-widest text-muted-foreground\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\nCommandShortcut.displayName = \"CommandShortcut\"\n\nexport {\n  Command,\n  CommandDialog,\n  CommandInput,\n  CommandList,\n  CommandEmpty,\n  CommandGroup,\n  CommandItem,\n  CommandShortcut,\n  CommandSeparator,\n}\n", "size_bytes": 4885}, "client/src/components/ui/context-menu.tsx": {"content": "import * as React from \"react\"\nimport * as ContextMenuPrimitive from \"@radix-ui/react-context-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ContextMenu = ContextMenuPrimitive.Root\n\nconst ContextMenuTrigger = ContextMenuPrimitive.Trigger\n\nconst ContextMenuGroup = ContextMenuPrimitive.Group\n\nconst ContextMenuPortal = ContextMenuPrimitive.Portal\n\nconst ContextMenuSub = ContextMenuPrimitive.Sub\n\nconst ContextMenuRadioGroup = ContextMenuPrimitive.RadioGroup\n\nconst ContextMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <ContextMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </ContextMenuPrimitive.SubTrigger>\n))\nContextMenuSubTrigger.displayName = ContextMenuPrimitive.SubTrigger.displayName\n\nconst ContextMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <ContextMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-context-menu-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nContextMenuSubContent.displayName = ContextMenuPrimitive.SubContent.displayName\n\nconst ContextMenuContent = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <ContextMenuPrimitive.Portal>\n    <ContextMenuPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"z-50 max-h-[--radix-context-menu-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md animate-in fade-in-80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-context-menu-content-transform-origin]\",\n        className\n      )}\n      {...props}\n    />\n  </ContextMenuPrimitive.Portal>\n))\nContextMenuContent.displayName = ContextMenuPrimitive.Content.displayName\n\nconst ContextMenuItem = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <ContextMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nContextMenuItem.displayName = ContextMenuPrimitive.Item.displayName\n\nconst ContextMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <ContextMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <ContextMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </ContextMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </ContextMenuPrimitive.CheckboxItem>\n))\nContextMenuCheckboxItem.displayName =\n  ContextMenuPrimitive.CheckboxItem.displayName\n\nconst ContextMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <ContextMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <ContextMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </ContextMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </ContextMenuPrimitive.RadioItem>\n))\nContextMenuRadioItem.displayName = ContextMenuPrimitive.RadioItem.displayName\n\nconst ContextMenuLabel = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <ContextMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold text-foreground\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nContextMenuLabel.displayName = ContextMenuPrimitive.Label.displayName\n\nconst ContextMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <ContextMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-border\", className)}\n    {...props}\n  />\n))\nContextMenuSeparator.displayName = ContextMenuPrimitive.Separator.displayName\n\nconst ContextMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\n        \"ml-auto text-xs tracking-widest text-muted-foreground\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\nContextMenuShortcut.displayName = \"ContextMenuShortcut\"\n\nexport {\n  ContextMenu,\n  ContextMenuTrigger,\n  ContextMenuContent,\n  ContextMenuItem,\n  ContextMenuCheckboxItem,\n  ContextMenuRadioItem,\n  ContextMenuLabel,\n  ContextMenuSeparator,\n  ContextMenuShortcut,\n  ContextMenuGroup,\n  ContextMenuPortal,\n  ContextMenuSub,\n  ContextMenuSubContent,\n  ContextMenuSubTrigger,\n  ContextMenuRadioGroup,\n}\n", "size_bytes": 7428}, "client/src/components/ui/dialog.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n", "size_bytes": 3848}, "client/src/components/ui/drawer.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport { Drawer as DrawerPrimitive } from \"vaul\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Drawer = ({\n  shouldScaleBackground = true,\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Root>) => (\n  <DrawerPrimitive.Root\n    shouldScaleBackground={shouldScaleBackground}\n    {...props}\n  />\n)\nDrawer.displayName = \"Drawer\"\n\nconst DrawerTrigger = DrawerPrimitive.Trigger\n\nconst DrawerPortal = DrawerPrimitive.Portal\n\nconst DrawerClose = DrawerPrimitive.Close\n\nconst DrawerOverlay = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Overlay\n    ref={ref}\n    className={cn(\"fixed inset-0 z-50 bg-black/80\", className)}\n    {...props}\n  />\n))\nDrawerOverlay.displayName = DrawerPrimitive.Overlay.displayName\n\nconst DrawerContent = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DrawerPortal>\n    <DrawerOverlay />\n    <DrawerPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto flex-col rounded-t-[10px] border bg-background\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"mx-auto mt-4 h-2 w-[100px] rounded-full bg-muted\" />\n      {children}\n    </DrawerPrimitive.Content>\n  </DrawerPortal>\n))\nDrawerContent.displayName = \"DrawerContent\"\n\nconst DrawerHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\"grid gap-1.5 p-4 text-center sm:text-left\", className)}\n    {...props}\n  />\n)\nDrawerHeader.displayName = \"DrawerHeader\"\n\nconst DrawerFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n    {...props}\n  />\n)\nDrawerFooter.displayName = \"DrawerFooter\"\n\nconst DrawerTitle = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDrawerTitle.displayName = DrawerPrimitive.Title.displayName\n\nconst DrawerDescription = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDrawerDescription.displayName = DrawerPrimitive.Description.displayName\n\nexport {\n  Drawer,\n  DrawerPortal,\n  DrawerOverlay,\n  DrawerTrigger,\n  DrawerClose,\n  DrawerContent,\n  DrawerHeader,\n  DrawerFooter,\n  DrawerTitle,\n  DrawerDescription,\n}\n", "size_bytes": 3021}, "client/src/components/ui/dropdown-menu.tsx": {"content": "import * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n", "size_bytes": 7609}, "client/src/components/ui/form.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState, formState } = useFormContext()\n\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nconst FormItem = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div ref={ref} className={cn(\"space-y-2\", className)} {...props} />\n    </FormItemContext.Provider>\n  )\n})\nFormItem.displayName = \"FormItem\"\n\nconst FormLabel = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      ref={ref}\n      className={cn(error && \"text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n})\nFormLabel.displayName = \"FormLabel\"\n\nconst FormControl = React.forwardRef<\n  React.ElementRef<typeof Slot>,\n  React.ComponentPropsWithoutRef<typeof Slot>\n>(({ ...props }, ref) => {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      ref={ref}\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n})\nFormControl.displayName = \"FormControl\"\n\nconst FormDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      ref={ref}\n      id={formDescriptionId}\n      className={cn(\"text-sm text-muted-foreground\", className)}\n      {...props}\n    />\n  )\n})\nFormDescription.displayName = \"FormDescription\"\n\nconst FormMessage = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      ref={ref}\n      id={formMessageId}\n      className={cn(\"text-sm font-medium text-destructive\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n})\nFormMessage.displayName = \"FormMessage\"\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n", "size_bytes": 4120}, "client/src/components/ui/hover-card.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as HoverCardPrimitive from \"@radix-ui/react-hover-card\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst HoverCard = HoverCardPrimitive.Root\n\nconst HoverCardTrigger = HoverCardPrimitive.Trigger\n\nconst HoverCardContent = React.forwardRef<\n  React.ElementRef<typeof HoverCardPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof HoverCardPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <HoverCardPrimitive.Content\n    ref={ref}\n    align={align}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 w-64 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-hover-card-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nHoverCardContent.displayName = HoverCardPrimitive.Content.displayName\n\nexport { HoverCard, HoverCardTrigger, HoverCardContent }\n", "size_bytes": 1251}, "client/src/components/ui/input-otp.tsx": {"content": "import * as React from \"react\"\nimport { OTPInput, OTPInputContext } from \"input-otp\"\nimport { Dot } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst InputOTP = React.forwardRef<\n  React.ElementRef<typeof OTPInput>,\n  React.ComponentPropsWithoutRef<typeof OTPInput>\n>(({ className, containerClassName, ...props }, ref) => (\n  <OTPInput\n    ref={ref}\n    containerClassName={cn(\n      \"flex items-center gap-2 has-[:disabled]:opacity-50\",\n      containerClassName\n    )}\n    className={cn(\"disabled:cursor-not-allowed\", className)}\n    {...props}\n  />\n))\nInputOTP.displayName = \"InputOTP\"\n\nconst InputOTPGroup = React.forwardRef<\n  React.ElementRef<\"div\">,\n  React.ComponentPropsWithoutRef<\"div\">\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center\", className)} {...props} />\n))\nInputOTPGroup.displayName = \"InputOTPGroup\"\n\nconst InputOTPSlot = React.forwardRef<\n  React.ElementRef<\"div\">,\n  React.ComponentPropsWithoutRef<\"div\"> & { index: number }\n>(({ index, className, ...props }, ref) => {\n  const inputOTPContext = React.useContext(OTPInputContext)\n  const { char, hasFakeCaret, isActive } = inputOTPContext.slots[index]\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        \"relative flex h-10 w-10 items-center justify-center border-y border-r border-input text-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md\",\n        isActive && \"z-10 ring-2 ring-ring ring-offset-background\",\n        className\n      )}\n      {...props}\n    >\n      {char}\n      {hasFakeCaret && (\n        <div className=\"pointer-events-none absolute inset-0 flex items-center justify-center\">\n          <div className=\"h-4 w-px animate-caret-blink bg-foreground duration-1000\" />\n        </div>\n      )}\n    </div>\n  )\n})\nInputOTPSlot.displayName = \"InputOTPSlot\"\n\nconst InputOTPSeparator = React.forwardRef<\n  React.ElementRef<\"div\">,\n  React.ComponentPropsWithoutRef<\"div\">\n>(({ ...props }, ref) => (\n  <div ref={ref} role=\"separator\" {...props}>\n    <Dot />\n  </div>\n))\nInputOTPSeparator.displayName = \"InputOTPSeparator\"\n\nexport { InputOTP, InputOTPGroup, InputOTPSlot, InputOTPSeparator }\n", "size_bytes": 2154}, "client/src/components/ui/input.tsx": {"content": "import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n", "size_bytes": 791}, "client/src/components/ui/label.tsx": {"content": "import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n", "size_bytes": 710}, "client/src/components/ui/menubar.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as MenubarPrimitive from \"@radix-ui/react-menubar\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction MenubarMenu({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Menu>) {\n  return <MenubarPrimitive.Menu {...props} />\n}\n\nfunction MenubarGroup({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Group>) {\n  return <MenubarPrimitive.Group {...props} />\n}\n\nfunction MenubarPortal({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Portal>) {\n  return <MenubarPrimitive.Portal {...props} />\n}\n\nfunction MenubarRadioGroup({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.RadioGroup>) {\n  return <MenubarPrimitive.RadioGroup {...props} />\n}\n\nfunction MenubarSub({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Sub>) {\n  return <MenubarPrimitive.Sub data-slot=\"menubar-sub\" {...props} />\n}\n\nconst Menubar = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <MenubarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"flex h-10 items-center space-x-1 rounded-md border bg-background p-1\",\n      className\n    )}\n    {...props}\n  />\n))\nMenubar.displayName = MenubarPrimitive.Root.displayName\n\nconst MenubarTrigger = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <MenubarPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-3 py-1.5 text-sm font-medium outline-none focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nMenubarTrigger.displayName = MenubarPrimitive.Trigger.displayName\n\nconst MenubarSubTrigger = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <MenubarPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </MenubarPrimitive.SubTrigger>\n))\nMenubarSubTrigger.displayName = MenubarPrimitive.SubTrigger.displayName\n\nconst MenubarSubContent = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <MenubarPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-menubar-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nMenubarSubContent.displayName = MenubarPrimitive.SubContent.displayName\n\nconst MenubarContent = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Content>\n>(\n  (\n    { className, align = \"start\", alignOffset = -4, sideOffset = 8, ...props },\n    ref\n  ) => (\n    <MenubarPrimitive.Portal>\n      <MenubarPrimitive.Content\n        ref={ref}\n        align={align}\n        alignOffset={alignOffset}\n        sideOffset={sideOffset}\n        className={cn(\n          \"z-50 min-w-[12rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-menubar-content-transform-origin]\",\n          className\n        )}\n        {...props}\n      />\n    </MenubarPrimitive.Portal>\n  )\n)\nMenubarContent.displayName = MenubarPrimitive.Content.displayName\n\nconst MenubarItem = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <MenubarPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nMenubarItem.displayName = MenubarPrimitive.Item.displayName\n\nconst MenubarCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <MenubarPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <MenubarPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </MenubarPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </MenubarPrimitive.CheckboxItem>\n))\nMenubarCheckboxItem.displayName = MenubarPrimitive.CheckboxItem.displayName\n\nconst MenubarRadioItem = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <MenubarPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <MenubarPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </MenubarPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </MenubarPrimitive.RadioItem>\n))\nMenubarRadioItem.displayName = MenubarPrimitive.RadioItem.displayName\n\nconst MenubarLabel = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <MenubarPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nMenubarLabel.displayName = MenubarPrimitive.Label.displayName\n\nconst MenubarSeparator = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <MenubarPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nMenubarSeparator.displayName = MenubarPrimitive.Separator.displayName\n\nconst MenubarShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\n        \"ml-auto text-xs tracking-widest text-muted-foreground\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\nMenubarShortcut.displayname = \"MenubarShortcut\"\n\nexport {\n  Menubar,\n  MenubarMenu,\n  MenubarTrigger,\n  MenubarContent,\n  MenubarItem,\n  MenubarSeparator,\n  MenubarLabel,\n  MenubarCheckboxItem,\n  MenubarRadioGroup,\n  MenubarRadioItem,\n  MenubarPortal,\n  MenubarSubContent,\n  MenubarSubTrigger,\n  MenubarGroup,\n  MenubarSub,\n  MenubarShortcut,\n}\n", "size_bytes": 8605}, "client/src/components/ui/navigation-menu.tsx": {"content": "import * as React from \"react\"\nimport * as NavigationMenuPrimitive from \"@radix-ui/react-navigation-menu\"\nimport { cva } from \"class-variance-authority\"\nimport { ChevronDown } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst NavigationMenu = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <NavigationMenuPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative z-10 flex max-w-max flex-1 items-center justify-center\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <NavigationMenuViewport />\n  </NavigationMenuPrimitive.Root>\n))\nNavigationMenu.displayName = NavigationMenuPrimitive.Root.displayName\n\nconst NavigationMenuList = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <NavigationMenuPrimitive.List\n    ref={ref}\n    className={cn(\n      \"group flex flex-1 list-none items-center justify-center space-x-1\",\n      className\n    )}\n    {...props}\n  />\n))\nNavigationMenuList.displayName = NavigationMenuPrimitive.List.displayName\n\nconst NavigationMenuItem = NavigationMenuPrimitive.Item\n\nconst navigationMenuTriggerStyle = cva(\n  \"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[state=open]:text-accent-foreground data-[state=open]:bg-accent/50 data-[state=open]:hover:bg-accent data-[state=open]:focus:bg-accent\"\n)\n\nconst NavigationMenuTrigger = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <NavigationMenuPrimitive.Trigger\n    ref={ref}\n    className={cn(navigationMenuTriggerStyle(), \"group\", className)}\n    {...props}\n  >\n    {children}{\" \"}\n    <ChevronDown\n      className=\"relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180\"\n      aria-hidden=\"true\"\n    />\n  </NavigationMenuPrimitive.Trigger>\n))\nNavigationMenuTrigger.displayName = NavigationMenuPrimitive.Trigger.displayName\n\nconst NavigationMenuContent = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <NavigationMenuPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"left-0 top-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto \",\n      className\n    )}\n    {...props}\n  />\n))\nNavigationMenuContent.displayName = NavigationMenuPrimitive.Content.displayName\n\nconst NavigationMenuLink = NavigationMenuPrimitive.Link\n\nconst NavigationMenuViewport = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Viewport>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Viewport>\n>(({ className, ...props }, ref) => (\n  <div className={cn(\"absolute left-0 top-full flex justify-center\")}>\n    <NavigationMenuPrimitive.Viewport\n      className={cn(\n        \"origin-top-center relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)]\",\n        className\n      )}\n      ref={ref}\n      {...props}\n    />\n  </div>\n))\nNavigationMenuViewport.displayName =\n  NavigationMenuPrimitive.Viewport.displayName\n\nconst NavigationMenuIndicator = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Indicator>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Indicator>\n>(({ className, ...props }, ref) => (\n  <NavigationMenuPrimitive.Indicator\n    ref={ref}\n    className={cn(\n      \"top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in\",\n      className\n    )}\n    {...props}\n  >\n    <div className=\"relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md\" />\n  </NavigationMenuPrimitive.Indicator>\n))\nNavigationMenuIndicator.displayName =\n  NavigationMenuPrimitive.Indicator.displayName\n\nexport {\n  navigationMenuTriggerStyle,\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n}\n", "size_bytes": 5128}, "client/src/components/ui/pagination.tsx": {"content": "import * as React from \"react\"\nimport { ChevronLeft, ChevronRight, MoreHorizontal } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\nimport { ButtonProps, buttonVariants } from \"@/components/ui/button\"\n\nconst Pagination = ({ className, ...props }: React.ComponentProps<\"nav\">) => (\n  <nav\n    role=\"navigation\"\n    aria-label=\"pagination\"\n    className={cn(\"mx-auto flex w-full justify-center\", className)}\n    {...props}\n  />\n)\nPagination.displayName = \"Pagination\"\n\nconst PaginationContent = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    className={cn(\"flex flex-row items-center gap-1\", className)}\n    {...props}\n  />\n))\nPaginationContent.displayName = \"PaginationContent\"\n\nconst PaginationItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ className, ...props }, ref) => (\n  <li ref={ref} className={cn(\"\", className)} {...props} />\n))\nPaginationItem.displayName = \"PaginationItem\"\n\ntype PaginationLinkProps = {\n  isActive?: boolean\n} & Pick<ButtonProps, \"size\"> &\n  React.ComponentProps<\"a\">\n\nconst PaginationLink = ({\n  className,\n  isActive,\n  size = \"icon\",\n  ...props\n}: PaginationLinkProps) => (\n  <a\n    aria-current={isActive ? \"page\" : undefined}\n    className={cn(\n      buttonVariants({\n        variant: isActive ? \"outline\" : \"ghost\",\n        size,\n      }),\n      className\n    )}\n    {...props}\n  />\n)\nPaginationLink.displayName = \"PaginationLink\"\n\nconst PaginationPrevious = ({\n  className,\n  ...props\n}: React.ComponentProps<typeof PaginationLink>) => (\n  <PaginationLink\n    aria-label=\"Go to previous page\"\n    size=\"default\"\n    className={cn(\"gap-1 pl-2.5\", className)}\n    {...props}\n  >\n    <ChevronLeft className=\"h-4 w-4\" />\n    <span>Previous</span>\n  </PaginationLink>\n)\nPaginationPrevious.displayName = \"PaginationPrevious\"\n\nconst PaginationNext = ({\n  className,\n  ...props\n}: React.ComponentProps<typeof PaginationLink>) => (\n  <PaginationLink\n    aria-label=\"Go to next page\"\n    size=\"default\"\n    className={cn(\"gap-1 pr-2.5\", className)}\n    {...props}\n  >\n    <span>Next</span>\n    <ChevronRight className=\"h-4 w-4\" />\n  </PaginationLink>\n)\nPaginationNext.displayName = \"PaginationNext\"\n\nconst PaginationEllipsis = ({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) => (\n  <span\n    aria-hidden\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\n    {...props}\n  >\n    <MoreHorizontal className=\"h-4 w-4\" />\n    <span className=\"sr-only\">More pages</span>\n  </span>\n)\nPaginationEllipsis.displayName = \"PaginationEllipsis\"\n\nexport {\n  Pagination,\n  PaginationContent,\n  PaginationEllipsis,\n  PaginationItem,\n  PaginationLink,\n  PaginationNext,\n  PaginationPrevious,\n}\n", "size_bytes": 2751}, "client/src/components/ui/popover.tsx": {"content": "import * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Popover = PopoverPrimitive.Root\n\nconst PopoverTrigger = PopoverPrimitive.Trigger\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]\",\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n))\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\nexport { Popover, PopoverTrigger, PopoverContent }\n", "size_bytes": 1280}, "client/src/components/ui/progress.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n", "size_bytes": 791}, "client/src/components/ui/radio-group.tsx": {"content": "import * as React from \"react\"\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\nimport { Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst RadioGroup = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Root\n      className={cn(\"grid gap-2\", className)}\n      {...props}\n      ref={ref}\n    />\n  )\n})\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\n\nconst RadioGroupItem = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Item\n      ref={ref}\n      className={cn(\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\n      </RadioGroupPrimitive.Indicator>\n    </RadioGroupPrimitive.Item>\n  )\n})\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\n\nexport { RadioGroup, RadioGroupItem }\n", "size_bytes": 1467}, "client/src/components/ui/resizable.tsx": {"content": "\"use client\"\n\nimport { GripVertical } from \"lucide-react\"\nimport * as ResizablePrimitive from \"react-resizable-panels\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ResizablePanelGroup = ({\n  className,\n  ...props\n}: React.ComponentProps<typeof ResizablePrimitive.PanelGroup>) => (\n  <ResizablePrimitive.PanelGroup\n    className={cn(\n      \"flex h-full w-full data-[panel-group-direction=vertical]:flex-col\",\n      className\n    )}\n    {...props}\n  />\n)\n\nconst ResizablePanel = ResizablePrimitive.Panel\n\nconst ResizableHandle = ({\n  withHandle,\n  className,\n  ...props\n}: React.ComponentProps<typeof ResizablePrimitive.PanelResizeHandle> & {\n  withHandle?: boolean\n}) => (\n  <ResizablePrimitive.PanelResizeHandle\n    className={cn(\n      \"relative flex w-px items-center justify-center bg-border after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-1 data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:-translate-y-1/2 data-[panel-group-direction=vertical]:after:translate-x-0 [&[data-panel-group-direction=vertical]>div]:rotate-90\",\n      className\n    )}\n    {...props}\n  >\n    {withHandle && (\n      <div className=\"z-10 flex h-4 w-3 items-center justify-center rounded-sm border bg-border\">\n        <GripVertical className=\"h-2.5 w-2.5\" />\n      </div>\n    )}\n  </ResizablePrimitive.PanelResizeHandle>\n)\n\nexport { ResizablePanelGroup, ResizablePanel, ResizableHandle }\n", "size_bytes": 1723}, "client/src/components/ui/scroll-area.tsx": {"content": "import * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n", "size_bytes": 1642}, "client/src/components/ui/select.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n", "size_bytes": 5742}, "client/src/components/ui/separator.tsx": {"content": "import * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }\n", "size_bytes": 756}, "client/src/components/ui/sheet.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Sheet = SheetPrimitive.Root\n\nconst SheetTrigger = SheetPrimitive.Trigger\n\nconst SheetClose = SheetPrimitive.Close\n\nconst SheetPortal = SheetPrimitive.Portal\n\nconst SheetOverlay = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\n\nconst sheetVariants = cva(\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n  {\n    variants: {\n      side: {\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n        bottom:\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n        right:\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\n      },\n    },\n    defaultVariants: {\n      side: \"right\",\n    },\n  }\n)\n\ninterface SheetContentProps\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\n    VariantProps<typeof sheetVariants> {}\n\nconst SheetContent = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Content>,\n  SheetContentProps\n>(({ side = \"right\", className, children, ...props }, ref) => (\n  <SheetPortal>\n    <SheetOverlay />\n    <SheetPrimitive.Content\n      ref={ref}\n      className={cn(sheetVariants({ side }), className)}\n      {...props}\n    >\n      {children}\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </SheetPrimitive.Close>\n    </SheetPrimitive.Content>\n  </SheetPortal>\n))\nSheetContent.displayName = SheetPrimitive.Content.displayName\n\nconst SheetHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetHeader.displayName = \"SheetHeader\"\n\nconst SheetFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetFooter.displayName = \"SheetFooter\"\n\nconst SheetTitle = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\n    {...props}\n  />\n))\nSheetTitle.displayName = SheetPrimitive.Title.displayName\n\nconst SheetDescription = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nSheetDescription.displayName = SheetPrimitive.Description.displayName\n\nexport {\n  Sheet,\n  SheetPortal,\n  SheetOverlay,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n", "size_bytes": 4281}, "client/src/components/ui/sidebar.tsx": {"content": "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { VariantProps, cva } from \"class-variance-authority\"\nimport { PanelLeft } from \"lucide-react\"\n\nimport { useIsMobile } from \"@/hooks/use-mobile\"\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Separator } from \"@/components/ui/separator\"\nimport {\n  Sheet,\n  She<PERSON><PERSON>ontent,\n  SheetDescription,\n  SheetHeader,\n  SheetTitle,\n} from \"@/components/ui/sheet\"\nimport { Skeleton } from \"@/components/ui/skeleton\"\nimport {\n  <PERSON>lt<PERSON>,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\"\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\nconst SIDEBAR_WIDTH = \"16rem\"\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\n\ntype SidebarContextProps = {\n  state: \"expanded\" | \"collapsed\"\n  open: boolean\n  setOpen: (open: boolean) => void\n  openMobile: boolean\n  setOpenMobile: (open: boolean) => void\n  isMobile: boolean\n  toggleSidebar: () => void\n}\n\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null)\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext)\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\n  }\n\n  return context\n}\n\nconst SidebarProvider = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    defaultOpen?: boolean\n    open?: boolean\n    onOpenChange?: (open: boolean) => void\n  }\n>(\n  (\n    {\n      defaultOpen = true,\n      open: openProp,\n      onOpenChange: setOpenProp,\n      className,\n      style,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const isMobile = useIsMobile()\n    const [openMobile, setOpenMobile] = React.useState(false)\n\n    // This is the internal state of the sidebar.\n    // We use openProp and setOpenProp for control from outside the component.\n    const [_open, _setOpen] = React.useState(defaultOpen)\n    const open = openProp ?? _open\n    const setOpen = React.useCallback(\n      (value: boolean | ((value: boolean) => boolean)) => {\n        const openState = typeof value === \"function\" ? value(open) : value\n        if (setOpenProp) {\n          setOpenProp(openState)\n        } else {\n          _setOpen(openState)\n        }\n\n        // This sets the cookie to keep the sidebar state.\n        document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\n      },\n      [setOpenProp, open]\n    )\n\n    // Helper to toggle the sidebar.\n    const toggleSidebar = React.useCallback(() => {\n      return isMobile\n        ? setOpenMobile((open) => !open)\n        : setOpen((open) => !open)\n    }, [isMobile, setOpen, setOpenMobile])\n\n    // Adds a keyboard shortcut to toggle the sidebar.\n    React.useEffect(() => {\n      const handleKeyDown = (event: KeyboardEvent) => {\n        if (\n          event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\n          (event.metaKey || event.ctrlKey)\n        ) {\n          event.preventDefault()\n          toggleSidebar()\n        }\n      }\n\n      window.addEventListener(\"keydown\", handleKeyDown)\n      return () => window.removeEventListener(\"keydown\", handleKeyDown)\n    }, [toggleSidebar])\n\n    // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n    // This makes it easier to style the sidebar with Tailwind classes.\n    const state = open ? \"expanded\" : \"collapsed\"\n\n    const contextValue = React.useMemo<SidebarContextProps>(\n      () => ({\n        state,\n        open,\n        setOpen,\n        isMobile,\n        openMobile,\n        setOpenMobile,\n        toggleSidebar,\n      }),\n      [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\n    )\n\n    return (\n      <SidebarContext.Provider value={contextValue}>\n        <TooltipProvider delayDuration={0}>\n          <div\n            style={\n              {\n                \"--sidebar-width\": SIDEBAR_WIDTH,\n                \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n                ...style,\n              } as React.CSSProperties\n            }\n            className={cn(\n              \"group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar\",\n              className\n            )}\n            ref={ref}\n            {...props}\n          >\n            {children}\n          </div>\n        </TooltipProvider>\n      </SidebarContext.Provider>\n    )\n  }\n)\nSidebarProvider.displayName = \"SidebarProvider\"\n\nconst Sidebar = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    side?: \"left\" | \"right\"\n    variant?: \"sidebar\" | \"floating\" | \"inset\"\n    collapsible?: \"offcanvas\" | \"icon\" | \"none\"\n  }\n>(\n  (\n    {\n      side = \"left\",\n      variant = \"sidebar\",\n      collapsible = \"offcanvas\",\n      className,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\n\n    if (collapsible === \"none\") {\n      return (\n        <div\n          className={cn(\n            \"flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground\",\n            className\n          )}\n          ref={ref}\n          {...props}\n        >\n          {children}\n        </div>\n      )\n    }\n\n    if (isMobile) {\n      return (\n        <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n          <SheetContent\n            data-sidebar=\"sidebar\"\n            data-mobile=\"true\"\n            className=\"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden\"\n            style={\n              {\n                \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\n              } as React.CSSProperties\n            }\n            side={side}\n          >\n            <SheetHeader className=\"sr-only\">\n              <SheetTitle>Sidebar</SheetTitle>\n              <SheetDescription>Displays the mobile sidebar.</SheetDescription>\n            </SheetHeader>\n            <div className=\"flex h-full w-full flex-col\">{children}</div>\n          </SheetContent>\n        </Sheet>\n      )\n    }\n\n    return (\n      <div\n        ref={ref}\n        className=\"group peer hidden text-sidebar-foreground md:block\"\n        data-state={state}\n        data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\n        data-variant={variant}\n        data-side={side}\n      >\n        {/* This is what handles the sidebar gap on desktop */}\n        <div\n          className={cn(\n            \"relative w-[--sidebar-width] bg-transparent transition-[width] duration-200 ease-linear\",\n            \"group-data-[collapsible=offcanvas]:w-0\",\n            \"group-data-[side=right]:rotate-180\",\n            variant === \"floating\" || variant === \"inset\"\n              ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]\"\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon]\"\n          )}\n        />\n        <div\n          className={cn(\n            \"fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] duration-200 ease-linear md:flex\",\n            side === \"left\"\n              ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\n              : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\n            // Adjust the padding for floating and inset variants.\n            variant === \"floating\" || variant === \"inset\"\n              ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]\"\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l\",\n            className\n          )}\n          {...props}\n        >\n          <div\n            data-sidebar=\"sidebar\"\n            className=\"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow\"\n          >\n            {children}\n          </div>\n        </div>\n      </div>\n    )\n  }\n)\nSidebar.displayName = \"Sidebar\"\n\nconst SidebarTrigger = React.forwardRef<\n  React.ElementRef<typeof Button>,\n  React.ComponentProps<typeof Button>\n>(({ className, onClick, ...props }, ref) => {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <Button\n      ref={ref}\n      data-sidebar=\"trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"h-7 w-7\", className)}\n      onClick={(event) => {\n        onClick?.(event)\n        toggleSidebar()\n      }}\n      {...props}\n    >\n      <PanelLeft />\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  )\n})\nSidebarTrigger.displayName = \"SidebarTrigger\"\n\nconst SidebarRail = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\">\n>(({ className, ...props }, ref) => {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <button\n      ref={ref}\n      data-sidebar=\"rail\"\n      aria-label=\"Toggle Sidebar\"\n      tabIndex={-1}\n      onClick={toggleSidebar}\n      title=\"Toggle Sidebar\"\n      className={cn(\n        \"absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex\",\n        \"[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize\",\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\n        \"group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar\",\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarRail.displayName = \"SidebarRail\"\n\nconst SidebarInset = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"main\">\n>(({ className, ...props }, ref) => {\n  return (\n    <main\n      ref={ref}\n      className={cn(\n        \"relative flex w-full flex-1 flex-col bg-background\",\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarInset.displayName = \"SidebarInset\"\n\nconst SidebarInput = React.forwardRef<\n  React.ElementRef<typeof Input>,\n  React.ComponentProps<typeof Input>\n>(({ className, ...props }, ref) => {\n  return (\n    <Input\n      ref={ref}\n      data-sidebar=\"input\"\n      className={cn(\n        \"h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarInput.displayName = \"SidebarInput\"\n\nconst SidebarHeader = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"header\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarHeader.displayName = \"SidebarHeader\"\n\nconst SidebarFooter = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"footer\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarFooter.displayName = \"SidebarFooter\"\n\nconst SidebarSeparator = React.forwardRef<\n  React.ElementRef<typeof Separator>,\n  React.ComponentProps<typeof Separator>\n>(({ className, ...props }, ref) => {\n  return (\n    <Separator\n      ref={ref}\n      data-sidebar=\"separator\"\n      className={cn(\"mx-2 w-auto bg-sidebar-border\", className)}\n      {...props}\n    />\n  )\n})\nSidebarSeparator.displayName = \"SidebarSeparator\"\n\nconst SidebarContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"content\"\n      className={cn(\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarContent.displayName = \"SidebarContent\"\n\nconst SidebarGroup = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"group\"\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarGroup.displayName = \"SidebarGroup\"\n\nconst SidebarGroupLabel = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"div\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-label\"\n      className={cn(\n        \"flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarGroupLabel.displayName = \"SidebarGroupLabel\"\n\nconst SidebarGroupAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-action\"\n      className={cn(\n        \"absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarGroupAction.displayName = \"SidebarGroupAction\"\n\nconst SidebarGroupContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    data-sidebar=\"group-content\"\n    className={cn(\"w-full text-sm\", className)}\n    {...props}\n  />\n))\nSidebarGroupContent.displayName = \"SidebarGroupContent\"\n\nconst SidebarMenu = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    data-sidebar=\"menu\"\n    className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\n    {...props}\n  />\n))\nSidebarMenu.displayName = \"SidebarMenu\"\n\nconst SidebarMenuItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    data-sidebar=\"menu-item\"\n    className={cn(\"group/menu-item relative\", className)}\n    {...props}\n  />\n))\nSidebarMenuItem.displayName = \"SidebarMenuItem\"\n\nconst sidebarMenuButtonVariants = cva(\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n        outline:\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\n      },\n      size: {\n        default: \"h-8 text-sm\",\n        sm: \"h-7 text-xs\",\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:!p-0\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst SidebarMenuButton = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & {\n    asChild?: boolean\n    isActive?: boolean\n    tooltip?: string | React.ComponentProps<typeof TooltipContent>\n  } & VariantProps<typeof sidebarMenuButtonVariants>\n>(\n  (\n    {\n      asChild = false,\n      isActive = false,\n      variant = \"default\",\n      size = \"default\",\n      tooltip,\n      className,\n      ...props\n    },\n    ref\n  ) => {\n    const Comp = asChild ? Slot : \"button\"\n    const { isMobile, state } = useSidebar()\n\n    const button = (\n      <Comp\n        ref={ref}\n        data-sidebar=\"menu-button\"\n        data-size={size}\n        data-active={isActive}\n        className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n        {...props}\n      />\n    )\n\n    if (!tooltip) {\n      return button\n    }\n\n    if (typeof tooltip === \"string\") {\n      tooltip = {\n        children: tooltip,\n      }\n    }\n\n    return (\n      <Tooltip>\n        <TooltipTrigger asChild>{button}</TooltipTrigger>\n        <TooltipContent\n          side=\"right\"\n          align=\"center\"\n          hidden={state !== \"collapsed\" || isMobile}\n          {...tooltip}\n        />\n      </Tooltip>\n    )\n  }\n)\nSidebarMenuButton.displayName = \"SidebarMenuButton\"\n\nconst SidebarMenuAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & {\n    asChild?: boolean\n    showOnHover?: boolean\n  }\n>(({ className, asChild = false, showOnHover = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-action\"\n      className={cn(\n        \"absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        showOnHover &&\n          \"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuAction.displayName = \"SidebarMenuAction\"\n\nconst SidebarMenuBadge = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    data-sidebar=\"menu-badge\"\n    className={cn(\n      \"pointer-events-none absolute right-1 flex h-5 min-w-5 select-none items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground\",\n      \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\n      \"peer-data-[size=sm]/menu-button:top-1\",\n      \"peer-data-[size=default]/menu-button:top-1.5\",\n      \"peer-data-[size=lg]/menu-button:top-2.5\",\n      \"group-data-[collapsible=icon]:hidden\",\n      className\n    )}\n    {...props}\n  />\n))\nSidebarMenuBadge.displayName = \"SidebarMenuBadge\"\n\nconst SidebarMenuSkeleton = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    showIcon?: boolean\n  }\n>(({ className, showIcon = false, ...props }, ref) => {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`\n  }, [])\n\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"menu-skeleton\"\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\n      {...props}\n    >\n      {showIcon && (\n        <Skeleton\n          className=\"size-4 rounded-md\"\n          data-sidebar=\"menu-skeleton-icon\"\n        />\n      )}\n      <Skeleton\n        className=\"h-4 max-w-[--skeleton-width] flex-1\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            \"--skeleton-width\": width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  )\n})\nSidebarMenuSkeleton.displayName = \"SidebarMenuSkeleton\"\n\nconst SidebarMenuSub = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    data-sidebar=\"menu-sub\"\n    className={cn(\n      \"mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5\",\n      \"group-data-[collapsible=icon]:hidden\",\n      className\n    )}\n    {...props}\n  />\n))\nSidebarMenuSub.displayName = \"SidebarMenuSub\"\n\nconst SidebarMenuSubItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ ...props }, ref) => <li ref={ref} {...props} />)\nSidebarMenuSubItem.displayName = \"SidebarMenuSubItem\"\n\nconst SidebarMenuSubButton = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentProps<\"a\"> & {\n    asChild?: boolean\n    size?: \"sm\" | \"md\"\n    isActive?: boolean\n  }\n>(({ asChild = false, size = \"md\", isActive, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        \"flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground\",\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\n        size === \"sm\" && \"text-xs\",\n        size === \"md\" && \"text-sm\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuSubButton.displayName = \"SidebarMenuSubButton\"\n\nexport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n}\n", "size_bytes": 23567}, "client/src/components/ui/skeleton.tsx": {"content": "import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n", "size_bytes": 261}, "client/src/components/ui/slider.tsx": {"content": "import * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Slider = React.forwardRef<\n  React.ElementRef<typeof SliderPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <SliderPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex w-full touch-none select-none items-center\",\n      className\n    )}\n    {...props}\n  >\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\">\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\n    </SliderPrimitive.Track>\n    <SliderPrimitive.Thumb className=\"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\" />\n  </SliderPrimitive.Root>\n))\nSlider.displayName = SliderPrimitive.Root.displayName\n\nexport { Slider }\n", "size_bytes": 1077}, "client/src/components/ui/switch.tsx": {"content": "import * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n", "size_bytes": 1139}, "client/src/components/ui/table.tsx": {"content": "import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"relative w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption\n    ref={ref}\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nTableCaption.displayName = \"TableCaption\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n", "size_bytes": 2765}, "client/src/components/ui/tabs.tsx": {"content": "import * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n", "size_bytes": 1883}, "client/src/components/ui/textarea.tsx": {"content": "import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Textarea = React.forwardRef<\n  HTMLTextAreaElement,\n  React.ComponentProps<\"textarea\">\n>(({ className, ...props }, ref) => {\n  return (\n    <textarea\n      className={cn(\n        \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      ref={ref}\n      {...props}\n    />\n  )\n})\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n", "size_bytes": 689}, "client/src/components/ui/toast.tsx": {"content": "import * as React from \"react\"\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ToastProvider = ToastPrimitives.Provider\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Viewport\n    ref={ref}\n    className={cn(\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\n      className\n    )}\n    {...props}\n  />\n))\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\n\nconst toastVariants = cva(\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\n  {\n    variants: {\n      variant: {\n        default: \"border bg-background text-foreground\",\n        destructive:\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\n    VariantProps<typeof toastVariants>\n>(({ className, variant, ...props }, ref) => {\n  return (\n    <ToastPrimitives.Root\n      ref={ref}\n      className={cn(toastVariants({ variant }), className)}\n      {...props}\n    />\n  )\n})\nToast.displayName = ToastPrimitives.Root.displayName\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Action\n    ref={ref}\n    className={cn(\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\n      className\n    )}\n    {...props}\n  />\n))\nToastAction.displayName = ToastPrimitives.Action.displayName\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Close\n    ref={ref}\n    className={cn(\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\n      className\n    )}\n    toast-close=\"\"\n    {...props}\n  >\n    <X className=\"h-4 w-4\" />\n  </ToastPrimitives.Close>\n))\nToastClose.displayName = ToastPrimitives.Close.displayName\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Title\n    ref={ref}\n    className={cn(\"text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nToastTitle.displayName = ToastPrimitives.Title.displayName\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Description\n    ref={ref}\n    className={cn(\"text-sm opacity-90\", className)}\n    {...props}\n  />\n))\nToastDescription.displayName = ToastPrimitives.Description.displayName\n\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\n\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\n\nexport {\n  type ToastProps,\n  type ToastActionElement,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n}\n", "size_bytes": 4845}, "client/src/components/ui/toaster.tsx": {"content": "import { useToast } from \"@/hooks/use-toast\"\nimport {\n  Toast,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n} from \"@/components/ui/toast\"\n\nexport function Toaster() {\n  const { toasts } = useToast()\n\n  return (\n    <ToastProvider>\n      {toasts.map(function ({ id, title, description, action, ...props }) {\n        return (\n          <Toast key={id} {...props}>\n            <div className=\"grid gap-1\">\n              {title && <ToastTitle>{title}</ToastTitle>}\n              {description && (\n                <ToastDescription>{description}</ToastDescription>\n              )}\n            </div>\n            {action}\n            <ToastClose />\n          </Toast>\n        )\n      })}\n      <ToastViewport />\n    </ToastProvider>\n  )\n}\n", "size_bytes": 772}, "client/src/components/ui/toggle-group.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as ToggleGroupPrimitive from \"@radix-ui/react-toggle-group\"\nimport { type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\nimport { toggleVariants } from \"@/components/ui/toggle\"\n\nconst ToggleGroupContext = React.createContext<\n  VariantProps<typeof toggleVariants>\n>({\n  size: \"default\",\n  variant: \"default\",\n})\n\nconst ToggleGroup = React.forwardRef<\n  React.ElementRef<typeof ToggleGroupPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Root> &\n    VariantProps<typeof toggleVariants>\n>(({ className, variant, size, children, ...props }, ref) => (\n  <ToggleGroupPrimitive.Root\n    ref={ref}\n    className={cn(\"flex items-center justify-center gap-1\", className)}\n    {...props}\n  >\n    <ToggleGroupContext.Provider value={{ variant, size }}>\n      {children}\n    </ToggleGroupContext.Provider>\n  </ToggleGroupPrimitive.Root>\n))\n\nToggleGroup.displayName = ToggleGroupPrimitive.Root.displayName\n\nconst ToggleGroupItem = React.forwardRef<\n  React.ElementRef<typeof ToggleGroupPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Item> &\n    VariantProps<typeof toggleVariants>\n>(({ className, children, variant, size, ...props }, ref) => {\n  const context = React.useContext(ToggleGroupContext)\n\n  return (\n    <ToggleGroupPrimitive.Item\n      ref={ref}\n      className={cn(\n        toggleVariants({\n          variant: context.variant || variant,\n          size: context.size || size,\n        }),\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </ToggleGroupPrimitive.Item>\n  )\n})\n\nToggleGroupItem.displayName = ToggleGroupPrimitive.Item.displayName\n\nexport { ToggleGroup, ToggleGroupItem }\n", "size_bytes": 1753}, "client/src/components/ui/toggle.tsx": {"content": "import * as React from \"react\"\nimport * as TogglePrimitive from \"@radix-ui/react-toggle\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst toggleVariants = cva(\n  \"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 gap-2\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-transparent\",\n        outline:\n          \"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground\",\n      },\n      size: {\n        default: \"h-10 px-3 min-w-10\",\n        sm: \"h-9 px-2.5 min-w-9\",\n        lg: \"h-11 px-5 min-w-11\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst Toggle = React.forwardRef<\n  React.ElementRef<typeof TogglePrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof TogglePrimitive.Root> &\n    VariantProps<typeof toggleVariants>\n>(({ className, variant, size, ...props }, ref) => (\n  <TogglePrimitive.Root\n    ref={ref}\n    className={cn(toggleVariants({ variant, size, className }))}\n    {...props}\n  />\n))\n\nToggle.displayName = TogglePrimitive.Root.displayName\n\nexport { Toggle, toggleVariants }\n", "size_bytes": 1527}, "client/src/components/ui/tooltip.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n", "size_bytes": 1209}}}